import { Pool } from 'pg';
import { Project } from '../models/Project';
import { ProjectData } from '../types';
import { ProjectManagementService as IProjectManagementService } from '../types/services';
import { AuthorizationService } from './AuthorizationService';
export declare class ProjectManagementError extends Error {
    readonly code: string;
    readonly projectId?: string;
    readonly userId?: string;
    constructor(message: string, code: string, projectId?: string, userId?: string);
}
export declare class ProjectManagementService implements IProjectManagementService {
    private db;
    private authService;
    constructor(database: Pool, authorizationService: AuthorizationService);
    createProject(projectData: ProjectData, createdBy: string): Promise<Project>;
    updateProject(projectId: string, updates: Partial<ProjectData>, updatedBy: string): Promise<Project>;
    getProject(projectId: string, userId?: string): Promise<Project>;
    listProjects(userId: string): Promise<Project[]>;
    archiveProject(projectId: string, archivedBy: string): Promise<void>;
    restoreProject(projectId: string, restoredBy: string): Promise<void>;
    addProjectPermission(projectId: string, userId: string, role: 'admin' | 'editor' | 'viewer', grantedBy: string): Promise<void>;
    removeProjectPermission(projectId: string, userId: string, removedBy: string): Promise<void>;
    getProjectStats(userId: string): Promise<{
        totalProjects: number;
        activeProjects: number;
        archivedProjects: number;
        ownedProjects: number;
        sharedProjects: number;
    }>;
    searchProjects(userId: string, query: string, includeArchived?: boolean): Promise<Project[]>;
}
//# sourceMappingURL=ProjectManagementService.d.ts.map