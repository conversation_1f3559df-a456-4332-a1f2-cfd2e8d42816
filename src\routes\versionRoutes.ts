import { Router, Request, Response } from 'express';
import { Pool } from 'pg';
import { VersionControlService } from '../services/VersionControlService';
import { auth } from '../middleware/auth';

export function createVersionRoutes(db: Pool): Router {
  const router = Router();
  
  // Initialize services
  const versionService = new VersionControlService(db);

  /**
   * GET /api/versions/files/:fileId
   * Get version history for a file
   */
  router.get('/files/:fileId', auth, async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      const versions = await versionService.getVersionHistory(fileId);

      res.json({
        versions: versions.map(version => ({
          id: version.id,
          version: version.version,
          createdAt: version.createdAt,
          createdBy: version.createdBy,
          changes: version.changes,
          isLocked: version.isLocked,
          isReleased: version.isReleased,
          filePath: version.filePath
        }))
      });

    } catch (error) {
      console.error('Get version history error:', error);
      res.status(500).json({
        error: {
          code: 'GET_VERSION_HISTORY_FAILED',
          message: 'Failed to get version history',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * GET /api/versions/files/:fileId/:version
   * Get a specific version
   */
  router.get('/files/:fileId/:version', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version } = req.params;

      const versionRecord = await versionService.getVersion(fileId, version);

      if (!versionRecord) {
        return res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'Version not found',
            timestamp: new Date().toISOString()
          }
        });
      }

      res.json({
        version: {
          id: versionRecord.id,
          version: versionRecord.version,
          createdAt: versionRecord.createdAt,
          createdBy: versionRecord.createdBy,
          changes: versionRecord.changes,
          isLocked: versionRecord.isLocked,
          isReleased: versionRecord.isReleased,
          filePath: versionRecord.filePath
        }
      });

    } catch (error) {
      console.error('Get version error:', error);
      res.status(500).json({
        error: {
          code: 'GET_VERSION_FAILED',
          message: 'Failed to get version',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * POST /api/versions/files/:fileId/:version/lock
   * Lock a version
   */
  router.post('/files/:fileId/:version/lock', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version } = req.params;

      await versionService.lockVersion(fileId, version);

      res.json({
        message: 'Version locked successfully'
      });

    } catch (error) {
      console.error('Lock version error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'Version not found or already locked',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'LOCK_VERSION_FAILED',
            message: 'Failed to lock version',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * DELETE /api/versions/files/:fileId/:version/lock
   * Unlock a version
   */
  router.delete('/files/:fileId/:version/lock', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version } = req.params;

      await versionService.unlockVersion(fileId, version);

      res.json({
        message: 'Version unlocked successfully'
      });

    } catch (error) {
      console.error('Unlock version error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'Version not found, already unlocked, or is released',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'UNLOCK_VERSION_FAILED',
            message: 'Failed to unlock version',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * POST /api/versions/files/:fileId/:version/release
   * Release a version
   */
  router.post('/files/:fileId/:version/release', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version } = req.params;

      await versionService.releaseVersion(fileId, version);

      res.json({
        message: 'Version released successfully'
      });

    } catch (error) {
      console.error('Release version error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'Version not found or already released',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'RELEASE_VERSION_FAILED',
            message: 'Failed to release version',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * GET /api/versions/files/:fileId/compare/:version1/:version2
   * Compare two versions
   */
  router.get('/files/:fileId/compare/:version1/:version2', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version1, version2 } = req.params;

      const comparison = await versionService.compareVersions(fileId, version1, version2);

      res.json({
        comparison: {
          added: comparison.added,
          removed: comparison.removed,
          modified: comparison.modified,
          details: comparison.details
        }
      });

    } catch (error) {
      console.error('Compare versions error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'One or both versions not found',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'COMPARE_VERSIONS_FAILED',
            message: 'Failed to compare versions',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  /**
   * GET /api/versions/files/:fileId/latest
   * Get the latest version
   */
  router.get('/files/:fileId/latest', auth, async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      const version = await versionService.getLatestVersion(fileId);

      if (!version) {
        return res.status(404).json({
          error: {
            code: 'NO_VERSIONS_FOUND',
            message: 'No versions found for this file',
            timestamp: new Date().toISOString()
          }
        });
      }

      res.json({
        version: {
          id: version.id,
          version: version.version,
          createdAt: version.createdAt,
          createdBy: version.createdBy,
          changes: version.changes,
          isLocked: version.isLocked,
          isReleased: version.isReleased,
          filePath: version.filePath
        }
      });

    } catch (error) {
      console.error('Get latest version error:', error);
      res.status(500).json({
        error: {
          code: 'GET_LATEST_VERSION_FAILED',
          message: 'Failed to get latest version',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * GET /api/versions/files/:fileId/released
   * Get all released versions
   */
  router.get('/files/:fileId/released', auth, async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      const versions = await versionService.getReleasedVersions(fileId);

      res.json({
        versions: versions.map(version => ({
          id: version.id,
          version: version.version,
          createdAt: version.createdAt,
          createdBy: version.createdBy,
          changes: version.changes,
          isLocked: version.isLocked,
          isReleased: version.isReleased,
          filePath: version.filePath
        }))
      });

    } catch (error) {
      console.error('Get released versions error:', error);
      res.status(500).json({
        error: {
          code: 'GET_RELEASED_VERSIONS_FAILED',
          message: 'Failed to get released versions',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * GET /api/versions/files/:fileId/stats
   * Get version statistics
   */
  router.get('/files/:fileId/stats', auth, async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;

      const stats = await versionService.getVersionStatistics(fileId);

      res.json({
        statistics: stats
      });

    } catch (error) {
      console.error('Get version statistics error:', error);
      res.status(500).json({
        error: {
          code: 'GET_VERSION_STATS_FAILED',
          message: 'Failed to get version statistics',
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  /**
   * PUT /api/versions/files/:fileId/:version/changes
   * Update version changes description
   */
  router.put('/files/:fileId/:version/changes', auth, async (req: Request, res: Response) => {
    try {
      const { fileId, version } = req.params;
      const { changes } = req.body;

      if (!changes || typeof changes !== 'string') {
        return res.status(400).json({
          error: {
            code: 'INVALID_CHANGES',
            message: 'Changes description is required and must be a string',
            timestamp: new Date().toISOString()
          }
        });
      }

      await versionService.updateVersionChanges(fileId, version, changes);

      res.json({
        message: 'Version changes updated successfully'
      });

    } catch (error) {
      console.error('Update version changes error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          error: {
            code: 'VERSION_NOT_FOUND',
            message: 'Version not found or is locked',
            timestamp: new Date().toISOString()
          }
        });
      } else {
        res.status(500).json({
          error: {
            code: 'UPDATE_CHANGES_FAILED',
            message: 'Failed to update version changes',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  });

  return router;
}
