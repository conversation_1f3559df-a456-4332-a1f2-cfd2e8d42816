{"name": "dxf-parser", "version": "1.1.2", "description": "Parse dxf files into a readable, logical js object.", "main": "./dist/dxf-parser.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"test": "mocha --require @babel/register test", "dev": "tsc -w & webpack --mode development", "prod": "tsc && webpack --mode production", "prepublishOnly": "npm run prod"}, "repository": {"type": "git", "url": "https://github.com/gdsestimating/dxf-parser.git"}, "bugs": {"url": "https://github.com/gdsestimating/dxf-parser/issues", "email": "<EMAIL>"}, "homepage": "https://github.com/gdsestimating/dxf-parser", "author": "GDS Storefront Estimating (gdsestimating.com)", "contributors": ["<PERSON>-<PERSON> <b<PERSON><EMAIL>>"], "license": "MIT", "devDependencies": {"@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/register": "^7.10.4", "approvals": "^3.0.5", "mocha": "^8.0.1", "should": "^13.2.3", "typescript": "^4.4.3", "webpack": "^5.52.1", "webpack-cli": "^4.8.0"}, "dependencies": {"loglevel": "^1.7.1"}, "keywords": ["dxf", "cad", "parser", "reader"], "files": ["dist", "src"]}