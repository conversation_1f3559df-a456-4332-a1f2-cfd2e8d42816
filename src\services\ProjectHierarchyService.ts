import { Pool } from 'pg';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import { ProjectManagementError } from './ProjectManagementService';
import { AuthorizationService } from './AuthorizationService';
import { User } from '../types';

const mkdir = promisify(fs.mkdir);
const rmdir = promisify(fs.rmdir);
const access = promisify(fs.access);

/**
 * Project folder structure interface
 */
export interface ProjectFolder {
  id: string;
  projectId: string;
  name: string;
  parentId?: string;
  path: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

/**
 * Project hierarchy management service
 * Handles folder structure creation and management for project organization
 */
export class ProjectHierarchyService {
  private db: Pool;
  private authService: AuthorizationService;
  private baseStoragePath: string;

  constructor(
    database: Pool, 
    authorizationService: AuthorizationService,
    storagePath: string = './storage/projects'
  ) {
    this.db = database;
    this.authService = authorizationService;
    this.baseStoragePath = storagePath;
  }

  /**
   * Creates the initial folder structure for a new project
   */
  async createProjectStructure(projectId: string, createdBy: string): Promise<void> {
    try {
      // Check if user has permission to create project structure
      const hasPermission = await this.authService.hasPermission(
        { id: createdBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to create project structure',
          'PERMISSION_DENIED',
          projectId,
          createdBy
        );
      }

      const projectPath = path.join(this.baseStoragePath, projectId);

      // Create main project directory
      await mkdir(projectPath, { recursive: true });

      // Create standard subdirectories
      const standardFolders = [
        { name: 'documents', description: 'Project documentation and specifications' },
        { name: 'schematics', description: 'E3.series schematic files' },
        { name: 'drawings', description: 'CAD drawings and DXF files' },
        { name: 'reports', description: 'Generated reports and exports' },
        { name: 'archives', description: 'Archived versions and backups' }
      ];

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        for (const folder of standardFolders) {
          const folderPath = path.join(projectPath, folder.name);
          await mkdir(folderPath, { recursive: true });

          // Insert folder record into database
          await client.query(
            `INSERT INTO project_folders (id, project_id, name, path, created_by, created_at, updated_at, metadata)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
            [
              crypto.randomUUID(),
              projectId,
              folder.name,
              folderPath,
              createdBy,
              new Date(),
              new Date(),
              JSON.stringify({ description: folder.description, isStandard: true })
            ]
          );
        }

        await client.query('COMMIT');

      } catch (error) {
        await client.query('ROLLBACK');
        // Clean up created directories on error
        try {
          await rmdir(projectPath, { recursive: true });
        } catch (cleanupError) {
          console.warn('Failed to cleanup project directory:', cleanupError);
        }
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to create project structure: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRUCTURE_ERROR',
        projectId,
        createdBy
      );
    }
  }

  /**
   * Creates a custom folder within a project
   */
  async createFolder(
    projectId: string,
    folderName: string,
    parentId: string | undefined,
    createdBy: string,
    metadata: Record<string, any> = {}
  ): Promise<ProjectFolder> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: createdBy } as User,
        'project',
        'write',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to create folder',
          'PERMISSION_DENIED',
          projectId,
          createdBy
        );
      }

      // Validate folder name
      if (!folderName || folderName.trim().length === 0) {
        throw new ProjectManagementError(
          'Folder name cannot be empty',
          'VALIDATION_ERROR',
          projectId,
          createdBy
        );
      }

      // Sanitize folder name
      const sanitizedName = folderName.replace(/[^a-zA-Z0-9_-]/g, '_');

      // Determine parent path
      let parentPath = path.join(this.baseStoragePath, projectId);
      
      if (parentId) {
        const parentResult = await this.db.query(
          'SELECT path FROM project_folders WHERE id = $1 AND project_id = $2',
          [parentId, projectId]
        );

        if (parentResult.rows.length === 0) {
          throw new ProjectManagementError(
            'Parent folder not found',
            'NOT_FOUND',
            projectId,
            createdBy
          );
        }

        parentPath = parentResult.rows[0].path;
      }

      const folderPath = path.join(parentPath, sanitizedName);

      // Check if folder already exists
      try {
        await access(folderPath);
        throw new ProjectManagementError(
          'Folder already exists',
          'ALREADY_EXISTS',
          projectId,
          createdBy
        );
      } catch (error: any) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }

      // Create physical directory
      await mkdir(folderPath, { recursive: true });

      // Insert folder record
      const folderId = crypto.randomUUID();
      const now = new Date();

      await this.db.query(
        `INSERT INTO project_folders (id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          folderId,
          projectId,
          sanitizedName,
          parentId || null,
          folderPath,
          createdBy,
          now,
          now,
          JSON.stringify(metadata)
        ]
      );

      const folder: ProjectFolder = {
        id: folderId,
        projectId,
        name: sanitizedName,
        path: folderPath,
        createdBy,
        createdAt: now,
        updatedAt: now,
        metadata
      };

      if (parentId !== undefined) {
        folder.parentId = parentId;
      }

      return folder;

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to create folder: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'FOLDER_ERROR',
        projectId,
        createdBy
      );
    }
  }

  /**
   * Gets the folder structure for a project
   */
  async getProjectStructure(projectId: string, userId: string): Promise<ProjectFolder[]> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: userId } as User,
        'project',
        'read',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to view project structure',
          'PERMISSION_DENIED',
          projectId,
          userId
        );
      }

      const result = await this.db.query(
        `SELECT id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata
         FROM project_folders 
         WHERE project_id = $1 
         ORDER BY parent_id NULLS FIRST, name`,
        [projectId]
      );

      return result.rows.map(row => ({
        id: row.id,
        projectId: row.project_id,
        name: row.name,
        parentId: row.parent_id,
        path: row.path,
        createdBy: row.created_by,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        metadata: row.metadata || {}
      }));

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to get project structure: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRUCTURE_ERROR',
        projectId,
        userId
      );
    }
  }

  /**
   * Deletes a folder and its contents
   */
  async deleteFolder(folderId: string, deletedBy: string): Promise<void> {
    try {
      // Get folder information
      const folderResult = await this.db.query(
        'SELECT * FROM project_folders WHERE id = $1',
        [folderId]
      );

      if (folderResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Folder not found',
          'NOT_FOUND',
          undefined,
          deletedBy
        );
      }

      const folder = folderResult.rows[0];

      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: deletedBy } as User,
        'project',
        'write',
        folder.project_id
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to delete folder',
          'PERMISSION_DENIED',
          folder.project_id,
          deletedBy
        );
      }

      // Check if folder is a standard folder
      const metadata = typeof folder.metadata === 'string'
        ? JSON.parse(folder.metadata)
        : (folder.metadata || {});
      if (metadata.isStandard) {
        throw new ProjectManagementError(
          'Cannot delete standard project folders',
          'INVALID_OPERATION',
          folder.project_id,
          deletedBy
        );
      }

      // Check if folder has children
      const childrenResult = await this.db.query(
        'SELECT COUNT(*) as count FROM project_folders WHERE parent_id = $1',
        [folderId]
      );

      if (parseInt(childrenResult.rows[0].count) > 0) {
        throw new ProjectManagementError(
          'Cannot delete folder with subfolders',
          'INVALID_OPERATION',
          folder.project_id,
          deletedBy
        );
      }

      // Check if folder contains files
      const filesResult = await this.db.query(
        'SELECT COUNT(*) as count FROM files WHERE folder_id = $1 AND is_deleted = FALSE',
        [folderId]
      );

      if (parseInt(filesResult.rows[0].count) > 0) {
        throw new ProjectManagementError(
          'Cannot delete folder containing files',
          'INVALID_OPERATION',
          folder.project_id,
          deletedBy
        );
      }

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Delete folder record
        await client.query('DELETE FROM project_folders WHERE id = $1', [folderId]);

        // Delete physical directory
        try {
          await rmdir(folder.path);
        } catch (fsError) {
          console.warn('Failed to delete physical folder:', fsError);
          // Continue with database transaction even if physical deletion fails
        }

        await client.query('COMMIT');

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to delete folder: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'DELETE_ERROR',
        undefined,
        deletedBy
      );
    }
  }
}
