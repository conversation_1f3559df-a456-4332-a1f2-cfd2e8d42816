{"version": 3, "file": "ProjectArchivalService.js", "sourceRoot": "", "sources": ["../../src/services/ProjectArchivalService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAA6B;AAC7B,uCAAyB;AACzB,+BAAiC;AACjC,yEAAoE;AAIpE,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACpC,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACxC,MAAM,OAAO,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACtC,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AA8BlC,MAAa,sBAAsB;IAKjC,YACE,QAAc,EACd,oBAA0C,EAC1C,qBAA6B,oBAAoB;QAEjD,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,SAAiB,EACjB,UAAkB,EAClB,cAAoD,QAAQ,EAC5D,MAAe;QAEf,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,UAAU,EAAU,EAC1B,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,6CAA6C,EAC7C,mBAAmB,EACnB,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACvC,sCAAsC,EACtC,CAAC,SAAS,CAAC,CACZ,CAAC;YAEF,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,iDAAsB,CAC9B,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAGtC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC,CAAC;YAE5F,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAG5B,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;gBACpE,MAAM,eAAe,GAAG;oBACtB,OAAO;oBACP,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,UAAU;oBACV,MAAM;oBACN,WAAW;iBACZ,CAAC;gBAEF,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAGpF,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAG1D,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAGtD,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAG5D,MAAM,aAAa,GAAkB;oBACnC,EAAE,EAAE,SAAS;oBACb,SAAS;oBACT,WAAW;oBACX,WAAW,EAAE,UAAU;oBACvB,UAAU;oBACV,UAAU,EAAE,IAAI,IAAI,EAAE;oBACtB,MAAM;oBACN,QAAQ,EAAE;wBACR,WAAW,EAAE,OAAO,CAAC,IAAI;wBACzB,iBAAiB,EAAE,OAAO,CAAC,UAAU;wBACrC,iBAAiB,EAAE,OAAO,CAAC,UAAU;wBACrC,SAAS,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;wBACpD,SAAS,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;qBACrD;iBACF,CAAC;gBAGF,MAAM,MAAM,CAAC,KAAK,CAChB;;mDAEyC,EACzC;oBACE,aAAa,CAAC,EAAE;oBAChB,aAAa,CAAC,SAAS;oBACvB,aAAa,CAAC,WAAW;oBACzB,aAAa,CAAC,WAAW;oBACzB,aAAa,CAAC,UAAU;oBACxB,aAAa,CAAC,UAAU;oBACxB,aAAa,CAAC,MAAM;oBACpB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC;iBACvC,CACF,CAAC;gBAGF,MAAM,MAAM,CAAC,KAAK,CAChB,gEAAgE,EAChE,CAAC,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,SAAS,CAAC,CACpC,CAAC;gBAEF,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAE7B,OAAO,aAAa,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAG/B,IAAI,CAAC;oBACH,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC/C,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE,YAAY,CAAC,CAAC;gBACrE,CAAC;gBAED,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,qCAAqC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC/F,eAAe,EACf,SAAS,EACT,UAAU,CACX,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE,UAAkB;QACzE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACzC,mEAAmE,EACnE,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QACtE,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,aAAa,EACb,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAC9C,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,UAAkB;QACrE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACrC,kEAAkE,EAClE,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;QACvE,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,iBAAiB,EACjB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAC1C,CAAC;QAGF,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAGlD,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;gBAElF,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzB,MAAM,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACzC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,yBAAyB,CAAC,SAAiB,EAAE,UAAkB;QAC3E,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAC3C,yDAAyD,EACzD,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC,CAAC;QAC1E,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CACzB,eAAe,EACf,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAChD,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,SAAiB;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,kFAAkF,EAClF,CAAC,SAAS,CAAC,CACZ,CAAC;QACF,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,SAAiB;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,qGAAqG,EACrG,CAAC,SAAS,CAAC,CACZ,CAAC;QACF,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,SAAiB,EACjB,UAAkB,EAClB,UAA0B;QACxB,YAAY,EAAE,IAAI;QAClB,gBAAgB,EAAE,IAAI;QACtB,kBAAkB,EAAE,IAAI;KACzB;QAED,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACvC,8CAA8C,EAC9C,CAAC,SAAS,CAAC,CACZ,CAAC;YAEF,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,iDAAsB,CAC9B,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAGtC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,UAAU,EAAU,EAC1B,QAAQ,EACR,OAAO,CACR,IAAI,OAAO,CAAC,WAAW,KAAK,UAAU,CAAC;YAExC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,6CAA6C,EAC7C,mBAAmB,EACnB,OAAO,CAAC,UAAU,EAClB,UAAU,CACX,CAAC;YACJ,CAAC;YAGD,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAEnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAG5B,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,UAAU,CAAC;gBAGhE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC;gBAC9E,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;gBAGtF,MAAM,MAAM,CAAC,KAAK,CAChB;;;8CAGoC,EACpC;oBACE,SAAS;oBACT,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3E,eAAe,CAAC,OAAO,CAAC,WAAW;oBACnC,eAAe,CAAC,OAAO,CAAC,UAAU;oBAClC,eAAe,CAAC,OAAO,CAAC,UAAU;oBAClC,IAAI,IAAI,EAAE;oBACV,QAAQ;oBACR,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;iBACjD,CACF,CAAC;gBAGF,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC7B,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC9E,CAAC;gBAGD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC1E,CAAC;gBAGD,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAChF,CAAC;gBAED,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAE7B,OAAO,SAAS,CAAC;YAEnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/B,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACxF,eAAe,EACf,SAAS,EACT,UAAU,CACX,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,WAAmB,EAAE,SAAiB,EAAE,MAAW;QACvF,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;YAErF,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,MAAM,MAAM,CAAC,KAAK,CAChB;;uCAE6B,EAC7B;oBACE,MAAM,CAAC,EAAE;oBACT,SAAS;oBACT,MAAM,CAAC,IAAI;oBACX,MAAM,CAAC,SAAS;oBAChB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC;oBACjD,MAAM,CAAC,UAAU;oBACjB,MAAM,CAAC,UAAU;oBACjB,MAAM,CAAC,UAAU;oBACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;iBAChC,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,WAAmB,EAAE,SAAiB,EAAE,MAAW;QACnF,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;QAExE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,CAAC;YACrF,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAExD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAE7B,MAAM,MAAM,CAAC,KAAK,CAChB;;uCAE6B,EAC7B;oBACE,IAAI,CAAC,EAAE;oBACP,SAAS;oBACT,IAAI,CAAC,SAAS;oBACd,IAAI,CAAC,IAAI;oBACT,IAAI,CAAC,aAAa;oBAClB,IAAI,CAAC,SAAS;oBACd,IAAI,CAAC,IAAI;oBACT,IAAI,CAAC,QAAQ;oBACb,IAAI,CAAC,WAAW;oBAChB,IAAI,CAAC,WAAW;oBAChB,IAAI,CAAC,eAAe;oBACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC7B,KAAK;iBACN,CACF,CAAC;gBAGF,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;oBAClF,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAEzD,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC;oBACzB,MAAM,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,yBAAyB,CAAC,WAAmB,EAAE,SAAiB,EAAE,MAAW;QACzF,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;YAEzF,KAAK,MAAM,UAAU,IAAI,eAAe,EAAE,CAAC;gBACzC,MAAM,MAAM,CAAC,KAAK,CAChB;;;uDAG6C,EAC7C;oBACE,UAAU,CAAC,EAAE;oBACb,SAAS;oBACT,UAAU,CAAC,OAAO;oBAClB,UAAU,CAAC,IAAI;oBACf,UAAU,CAAC,UAAU;oBACrB,UAAU,CAAC,UAAU;iBACtB,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;CACF;AAleD,wDAkeC"}