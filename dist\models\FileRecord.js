"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileRecord = void 0;
class FileRecord {
    constructor(data) {
        this.id = data.id;
        this.projectId = data.projectId;
        this.name = data.name;
        this.originalName = data.originalName;
        this.fileType = data.fileType;
        this.size = data.size;
        this.checksum = data.checksum;
        this.uploadedBy = data.uploadedBy;
        this.uploadedAt = data.uploadedAt;
        this.currentVersion = data.currentVersion;
        this.metadata = data.metadata;
        this.versions = data.versions;
    }
    static validate(data) {
        const errors = [];
        if (!data.projectId || typeof data.projectId !== 'string' || data.projectId.trim().length === 0) {
            errors.push('ProjectId is required and must be a non-empty string');
        }
        if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
            errors.push('File name is required and must be a non-empty string');
        }
        if (data.name && data.name.length > 255) {
            errors.push('File name must not exceed 255 characters');
        }
        if (!data.originalName || typeof data.originalName !== 'string' || data.originalName.trim().length === 0) {
            errors.push('Original file name is required and must be a non-empty string');
        }
        if (data.originalName && data.originalName.length > 255) {
            errors.push('Original file name must not exceed 255 characters');
        }
        if (!data.fileType || !['e3series', 'pdf', 'dxf'].includes(data.fileType)) {
            errors.push('File type must be "e3series", "pdf", or "dxf"');
        }
        if (data.size === undefined || data.size === null || typeof data.size !== 'number' || data.size < 0) {
            errors.push('File size is required and must be a non-negative number');
        }
        if (data.size && data.size > 1024 * 1024 * 1024) {
            errors.push('File size must not exceed 1GB');
        }
        if (!data.checksum || typeof data.checksum !== 'string' || data.checksum.trim().length === 0) {
            errors.push('File checksum is required and must be a non-empty string');
        }
        if (data.checksum && !/^[a-fA-F0-9]{64}$/.test(data.checksum)) {
            errors.push('Checksum must be a valid SHA-256 hash (64 hexadecimal characters)');
        }
        if (!data.uploadedBy || typeof data.uploadedBy !== 'string' || data.uploadedBy.trim().length === 0) {
            errors.push('UploadedBy is required and must be a non-empty string');
        }
        if (!data.uploadedAt || !(data.uploadedAt instanceof Date)) {
            errors.push('UploadedAt is required and must be a valid Date object');
        }
        if (!data.currentVersion || typeof data.currentVersion !== 'string' || data.currentVersion.trim().length === 0) {
            errors.push('Current version is required and must be a non-empty string');
        }
        if (data.currentVersion && !/^\d+\.\d+\.\d+$/.test(data.currentVersion)) {
            errors.push('Current version must follow semantic versioning format (e.g., "1.0.0")');
        }
        if (!data.metadata || typeof data.metadata !== 'object') {
            errors.push('Metadata is required and must be an object');
        }
        if (!data.versions || !Array.isArray(data.versions)) {
            errors.push('Versions must be an array');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    static create(data) {
        const now = new Date();
        const fileData = {
            id: data.id || crypto.randomUUID(),
            projectId: data.projectId,
            name: data.name,
            originalName: data.originalName,
            fileType: data.fileType,
            size: data.size,
            checksum: data.checksum,
            uploadedBy: data.uploadedBy,
            uploadedAt: now,
            currentVersion: '1.0.0',
            metadata: data.metadata || {},
            versions: []
        };
        const validation = FileRecord.validate(fileData);
        if (!validation.isValid) {
            throw new Error(`Invalid file record data: ${validation.errors.join(', ')}`);
        }
        return new FileRecord(fileData);
    }
    updateMetadata(metadata) {
        this.metadata = { ...this.metadata, ...metadata };
    }
    addVersion(version) {
        if (this.versions.some(v => v.version === version.version)) {
            throw new Error(`Version ${version.version} already exists for this file`);
        }
        this.versions.push(version);
        this.currentVersion = version.version;
    }
    getVersion(versionNumber) {
        return this.versions.find(v => v.version === versionNumber);
    }
    getLatestVersion() {
        if (this.versions.length === 0)
            return undefined;
        return this.versions.reduce((latest, current) => {
            const latestParts = latest.version.split('.').map(Number);
            const currentParts = current.version.split('.').map(Number);
            for (let i = 0; i < Math.max(latestParts.length, currentParts.length); i++) {
                const latestPart = latestParts[i] || 0;
                const currentPart = currentParts[i] || 0;
                if (currentPart > latestPart)
                    return current;
                if (latestPart > currentPart)
                    return latest;
            }
            return latest;
        });
    }
    hasLockedVersions() {
        return this.versions.some(v => v.isLocked);
    }
    hasReleasedVersions() {
        return this.versions.some(v => v.isReleased);
    }
    static validateFileName(fileName, fileType) {
        const extensions = {
            'e3series': ['.e3s', '.e3p', '.e3d'],
            'pdf': ['.pdf'],
            'dxf': ['.dxf']
        };
        const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return extensions[fileType].includes(fileExtension);
    }
}
exports.FileRecord = FileRecord;
//# sourceMappingURL=FileRecord.js.map