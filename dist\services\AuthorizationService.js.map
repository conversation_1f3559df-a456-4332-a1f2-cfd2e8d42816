{"version": 3, "file": "AuthorizationService.js", "sourceRoot": "", "sources": ["../../src/services/AuthorizationService.ts"], "names": [], "mappings": ";;;AAcA,MAAa,wBAAwB;IAGnC,YAAY,EAAQ;QAClB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAU,EAAE,QAAkB,EAAE,UAAsB,EAAE,UAAmB;QAE7F,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;YAE/B,KAAK,MAAM;gBAET,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;oBACpD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;YAE/B,KAAK,SAAS;gBACZ,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEvE,KAAK,MAAM;gBACT,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEpE;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc,EAAE,SAAiB;QAC3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;;OAKb,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;YAE9D,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,OAAO;gBACL,MAAM,EAAE,GAAG,CAAC,OAAO;gBACnB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,SAAS,EAAE,GAAG,CAAC,UAAU;aAC1B,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,MAAc,EAAE,IAAmC,EAAE,SAAiB;QACpH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAG5B,MAAM,aAAa,GAAG,2EAA2E,CAAC;YAClG,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;YAExE,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAE7B,MAAM,MAAM,CAAC,KAAK,CAChB,kIAAkI,EAClI,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CACrC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBAEN,MAAM,MAAM,CAAC,KAAK,CAChB,iGAAiG,EACjG,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CACrC,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE,MAAc;QAC7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,KAAK,CAChB,wEAAwE,EACxE,CAAC,SAAS,EAAE,MAAM,CAAC,CACpB,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;;;;OAOb,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEtD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7B,MAAM,EAAE,GAAG,CAAC,OAAO;gBACnB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,SAAS,EAAE,GAAG,CAAC,UAAU;aAC1B,CAAC,CAAC,CAAC;QACN,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAU,EAAE,UAAsB,EAAE,SAAkB;QACvF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,+CAA+C,CAAC;YACrE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEpE,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;gBAClF,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,eAAe,GAAG;;;OAGvB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAErC,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,MAAM;oBACT,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC1D,KAAK,OAAO;oBACV,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChD,KAAK,OAAO;oBACV,OAAO,QAAQ,KAAK,OAAO,CAAC;gBAC9B;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAU,EAAE,UAAsB,EAAE,MAAe;QACjF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,4CAA4C,CAAC;YAC/D,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;YAE3D,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAGhD,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QACtE,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;CACF;AA7MD,4DA6MC"}