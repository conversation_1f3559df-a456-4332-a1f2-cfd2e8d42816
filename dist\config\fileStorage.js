"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultFileStorageConfig = void 0;
exports.validateFileStorageConfig = validateFileStorageConfig;
exports.getFileTypeByExtension = getFileTypeByExtension;
exports.isFileTypeAllowed = isFileTypeAllowed;
exports.getMaxFileSizeForType = getMaxFileSizeForType;
exports.ensureDirectoriesExist = ensureDirectoriesExist;
const path = __importStar(require("path"));
exports.defaultFileStorageConfig = {
    baseStoragePath: process.env.FILE_STORAGE_PATH || path.join(process.cwd(), 'storage'),
    tempStoragePath: process.env.TEMP_STORAGE_PATH || path.join(process.cwd(), 'temp'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '1073741824'),
    maxTotalStorageSize: parseInt(process.env.MAX_TOTAL_STORAGE || '107374182400'),
    allowedFileTypes: {
        'e3series': {
            extensions: ['.e3s', '.e3p', '.e3d'],
            mimeTypes: ['application/octet-stream', 'application/x-e3series'],
            maxSize: parseInt(process.env.MAX_E3_FILE_SIZE || '536870912')
        },
        'pdf': {
            extensions: ['.pdf'],
            mimeTypes: ['application/pdf'],
            maxSize: parseInt(process.env.MAX_PDF_FILE_SIZE || '104857600')
        },
        'dxf': {
            extensions: ['.dxf'],
            mimeTypes: ['application/dxf', 'image/vnd.dxf', 'application/octet-stream'],
            maxSize: parseInt(process.env.MAX_DXF_FILE_SIZE || '52428800')
        }
    },
    enableVirusScanning: process.env.ENABLE_VIRUS_SCANNING === 'true',
    quarantinePath: process.env.QUARANTINE_PATH || path.join(process.cwd(), 'quarantine'),
    enableAutoCleanup: process.env.ENABLE_AUTO_CLEANUP !== 'false',
    cleanupIntervalHours: parseInt(process.env.CLEANUP_INTERVAL_HOURS || '24'),
    retainDeletedFilesHours: parseInt(process.env.RETAIN_DELETED_FILES_HOURS || '168'),
    enableBackup: process.env.ENABLE_BACKUP === 'true',
    backupPath: process.env.BACKUP_PATH || path.join(process.cwd(), 'backups'),
    backupRetentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30')
};
function validateFileStorageConfig(config) {
    const errors = [];
    if (!config.baseStoragePath || config.baseStoragePath.trim().length === 0) {
        errors.push('Base storage path is required');
    }
    if (!config.tempStoragePath || config.tempStoragePath.trim().length === 0) {
        errors.push('Temp storage path is required');
    }
    if (config.maxFileSize <= 0) {
        errors.push('Max file size must be greater than 0');
    }
    if (config.maxTotalStorageSize <= 0) {
        errors.push('Max total storage size must be greater than 0');
    }
    if (config.maxFileSize > config.maxTotalStorageSize) {
        errors.push('Max file size cannot be greater than max total storage size');
    }
    Object.entries(config.allowedFileTypes).forEach(([fileType, typeConfig]) => {
        if (!typeConfig.extensions || typeConfig.extensions.length === 0) {
            errors.push(`File type ${fileType} must have at least one extension`);
        }
        if (!typeConfig.mimeTypes || typeConfig.mimeTypes.length === 0) {
            errors.push(`File type ${fileType} must have at least one MIME type`);
        }
        if (typeConfig.maxSize <= 0) {
            errors.push(`File type ${fileType} max size must be greater than 0`);
        }
        if (typeConfig.maxSize > config.maxFileSize) {
            errors.push(`File type ${fileType} max size cannot be greater than global max file size`);
        }
    });
    if (config.cleanupIntervalHours <= 0) {
        errors.push('Cleanup interval hours must be greater than 0');
    }
    if (config.retainDeletedFilesHours < 0) {
        errors.push('Retain deleted files hours cannot be negative');
    }
    if (config.enableBackup) {
        if (!config.backupPath || config.backupPath.trim().length === 0) {
            errors.push('Backup path is required when backup is enabled');
        }
        if (config.backupRetentionDays <= 0) {
            errors.push('Backup retention days must be greater than 0');
        }
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function getFileTypeByExtension(extension, config) {
    const normalizedExtension = extension.toLowerCase();
    for (const [fileType, typeConfig] of Object.entries(config.allowedFileTypes)) {
        if (typeConfig.extensions.includes(normalizedExtension)) {
            return fileType;
        }
    }
    return null;
}
function isFileTypeAllowed(fileType, config) {
    return fileType in config.allowedFileTypes;
}
function getMaxFileSizeForType(fileType, config) {
    return config.allowedFileTypes[fileType]?.maxSize || config.maxFileSize;
}
async function ensureDirectoriesExist(config) {
    const fs = await Promise.resolve().then(() => __importStar(require('fs')));
    const mkdir = fs.promises.mkdir;
    const directories = [
        config.baseStoragePath,
        config.tempStoragePath,
        config.quarantinePath
    ];
    if (config.enableBackup) {
        directories.push(config.backupPath);
    }
    for (const dir of directories) {
        try {
            await mkdir(dir, { recursive: true });
        }
        catch (error) {
        }
    }
}
//# sourceMappingURL=fileStorage.js.map