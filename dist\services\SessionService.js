"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionServiceImpl = void 0;
class SessionServiceImpl {
    constructor(db, sessionExpiryHours = 24) {
        this.db = db;
        this.sessionExpiryHours = sessionExpiryHours;
    }
    async createSession(user, token, refreshToken, ipAddress, userAgent) {
        const client = await this.db.connect();
        try {
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + this.sessionExpiryHours);
            const insertQuery = `
        INSERT INTO user_sessions (user_id, token, refresh_token, expires_at, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, user_id, token, refresh_token, expires_at, created_at, last_accessed_at, ip_address, user_agent, is_active
      `;
            const result = await client.query(insertQuery, [
                user.id,
                token,
                refreshToken,
                expiresAt,
                ipAddress,
                userAgent
            ]);
            const row = result.rows[0];
            return {
                id: row.id,
                userId: row.user_id,
                token: row.token,
                refreshToken: row.refresh_token,
                expiresAt: row.expires_at,
                createdAt: row.created_at,
                lastAccessedAt: row.last_accessed_at,
                ipAddress: row.ip_address,
                userAgent: row.user_agent,
                isActive: row.is_active
            };
        }
        finally {
            client.release();
        }
    }
    async getSession(sessionId) {
        const client = await this.db.connect();
        try {
            const query = `
        SELECT id, user_id, token, refresh_token, expires_at, created_at, last_accessed_at, ip_address, user_agent, is_active
        FROM user_sessions
        WHERE id = $1 AND is_active = true AND expires_at > CURRENT_TIMESTAMP
      `;
            const result = await client.query(query, [sessionId]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            await client.query('UPDATE user_sessions SET last_accessed_at = CURRENT_TIMESTAMP WHERE id = $1', [sessionId]);
            return {
                id: row.id,
                userId: row.user_id,
                token: row.token,
                refreshToken: row.refresh_token,
                expiresAt: row.expires_at,
                createdAt: row.created_at,
                lastAccessedAt: new Date(),
                ipAddress: row.ip_address,
                userAgent: row.user_agent,
                isActive: row.is_active
            };
        }
        finally {
            client.release();
        }
    }
    async getSessionByToken(token) {
        const client = await this.db.connect();
        try {
            const query = `
        SELECT id, user_id, token, refresh_token, expires_at, created_at, last_accessed_at, ip_address, user_agent, is_active
        FROM user_sessions
        WHERE token = $1 AND is_active = true AND expires_at > CURRENT_TIMESTAMP
      `;
            const result = await client.query(query, [token]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            await client.query('UPDATE user_sessions SET last_accessed_at = CURRENT_TIMESTAMP WHERE id = $1', [row.id]);
            return {
                id: row.id,
                userId: row.user_id,
                token: row.token,
                refreshToken: row.refresh_token,
                expiresAt: row.expires_at,
                createdAt: row.created_at,
                lastAccessedAt: new Date(),
                ipAddress: row.ip_address,
                userAgent: row.user_agent,
                isActive: row.is_active
            };
        }
        finally {
            client.release();
        }
    }
    async refreshSession(refreshToken) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const sessionQuery = `
        SELECT id, user_id, expires_at
        FROM user_sessions
        WHERE refresh_token = $1 AND is_active = true AND expires_at > CURRENT_TIMESTAMP
      `;
            const sessionResult = await client.query(sessionQuery, [refreshToken]);
            if (sessionResult.rows.length === 0) {
                throw new Error('Invalid or expired refresh token');
            }
            const sessionData = sessionResult.rows[0];
            const newToken = this.generateNewToken();
            const newRefreshToken = this.generateNewRefreshToken();
            const newExpiresAt = new Date();
            newExpiresAt.setHours(newExpiresAt.getHours() + this.sessionExpiryHours);
            const updateQuery = `
        UPDATE user_sessions 
        SET token = $1, refresh_token = $2, expires_at = $3, last_accessed_at = CURRENT_TIMESTAMP
        WHERE id = $4
        RETURNING id, user_id, token, refresh_token, expires_at, created_at, last_accessed_at, ip_address, user_agent, is_active
      `;
            const updateResult = await client.query(updateQuery, [
                newToken,
                newRefreshToken,
                newExpiresAt,
                sessionData.id
            ]);
            await client.query('COMMIT');
            const row = updateResult.rows[0];
            const session = {
                id: row.id,
                userId: row.user_id,
                token: row.token,
                refreshToken: row.refresh_token,
                expiresAt: row.expires_at,
                createdAt: row.created_at,
                lastAccessedAt: row.last_accessed_at,
                ipAddress: row.ip_address,
                userAgent: row.user_agent,
                isActive: row.is_active
            };
            return { session, newToken };
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async invalidateSession(sessionId) {
        const client = await this.db.connect();
        try {
            await client.query('UPDATE user_sessions SET is_active = false WHERE id = $1', [sessionId]);
        }
        finally {
            client.release();
        }
    }
    async invalidateAllUserSessions(userId) {
        const client = await this.db.connect();
        try {
            await client.query('UPDATE user_sessions SET is_active = false WHERE user_id = $1', [userId]);
        }
        finally {
            client.release();
        }
    }
    async cleanupExpiredSessions() {
        const client = await this.db.connect();
        try {
            const result = await client.query('DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP OR is_active = false');
            return result.rowCount || 0;
        }
        finally {
            client.release();
        }
    }
    async getUserActiveSessions(userId) {
        const client = await this.db.connect();
        try {
            const query = `
        SELECT id, user_id, token, refresh_token, expires_at, created_at, last_accessed_at, ip_address, user_agent, is_active
        FROM user_sessions
        WHERE user_id = $1 AND is_active = true AND expires_at > CURRENT_TIMESTAMP
        ORDER BY last_accessed_at DESC
      `;
            const result = await client.query(query, [userId]);
            return result.rows.map(row => ({
                id: row.id,
                userId: row.user_id,
                token: row.token,
                refreshToken: row.refresh_token,
                expiresAt: row.expires_at,
                createdAt: row.created_at,
                lastAccessedAt: row.last_accessed_at,
                ipAddress: row.ip_address,
                userAgent: row.user_agent,
                isActive: row.is_active
            }));
        }
        finally {
            client.release();
        }
    }
    generateNewToken() {
        return `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateNewRefreshToken() {
        return `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.SessionServiceImpl = SessionServiceImpl;
//# sourceMappingURL=SessionService.js.map