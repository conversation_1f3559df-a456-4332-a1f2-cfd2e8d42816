import { Version as IVersion } from '../types';
export declare class Version implements IVersion {
    id: string;
    fileId: string;
    version: string;
    createdBy: string;
    createdAt: Date;
    changes: string;
    isLocked: boolean;
    isReleased: boolean;
    filePath: string;
    constructor(data: IVersion);
    static validate(data: Partial<IVersion>): {
        isValid: boolean;
        errors: string[];
    };
    static create(data: {
        fileId: string;
        version: string;
        createdBy: string;
        changes: string;
        filePath: string;
        id?: string;
        isLocked?: boolean;
        isReleased?: boolean;
    }): Version;
    lock(): void;
    unlock(): void;
    release(): void;
    updateChanges(changes: string): void;
    static compareVersions(version1: string, version2: string): number;
    static getNextVersion(currentVersion: string, type?: 'major' | 'minor' | 'patch'): string;
    static isValidVersionFormat(version: string): boolean;
}
//# sourceMappingURL=Version.d.ts.map