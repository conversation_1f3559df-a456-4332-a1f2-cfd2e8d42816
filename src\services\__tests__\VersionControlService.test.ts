import { Pool } from 'pg';
import { VersionControlService } from '../VersionControlService';
import { Version } from '../../models/Version';
import { VersionChanges } from '../../types';

// Mock the database
const mockDb = {
  connect: jest.fn(),
  query: jest.fn(),
} as unknown as Pool;

const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

describe('VersionControlService', () => {
  let service: VersionControlService;

  beforeEach(() => {
    service = new VersionControlService(mockDb);
    jest.clearAllMocks();
    (mockDb.connect as jest.Mock).mockResolvedValue(mockClient);
  });

  describe('createVersion', () => {
    it('should create a new version for a file', async () => {
      const fileId = 'test-file-id';
      const changes: VersionChanges = {
        description: 'Initial version',
        modifiedBy: 'user-id'
      };

      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // Latest version query (no existing versions)
        .mockResolvedValueOnce({ 
          rows: [{ name: 'test-file.e3', file_type: 'e3series' }] 
        }) // File info query
        .mockResolvedValueOnce({ rows: [] }) // Insert version
        .mockResolvedValueOnce({ rows: [] }) // Update file current version
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      const result = await service.createVersion(fileId, changes);

      expect(result).toBeInstanceOf(Version);
      expect(result.version).toBe('1.0.0');
      expect(result.fileId).toBe(fileId);
      expect(result.createdBy).toBe(changes.modifiedBy);
      expect(result.changes).toBe(changes.description);
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
    });

    it('should increment version number when existing versions exist', async () => {
      const fileId = 'test-file-id';
      const changes: VersionChanges = {
        description: 'Second version',
        modifiedBy: 'user-id'
      };

      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ 
          rows: [{ version: '1.0.0' }] 
        }) // Latest version query (existing version)
        .mockResolvedValueOnce({ 
          rows: [{ name: 'test-file.e3', file_type: 'e3series' }] 
        }) // File info query
        .mockResolvedValueOnce({ rows: [] }) // Insert version
        .mockResolvedValueOnce({ rows: [] }) // Update file current version
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      const result = await service.createVersion(fileId, changes);

      expect(result.version).toBe('1.0.1');
    });

    it('should rollback transaction on error', async () => {
      const fileId = 'test-file-id';
      const changes: VersionChanges = {
        description: 'Failed version',
        modifiedBy: 'user-id'
      };

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')); // Latest version query fails

      await expect(service.createVersion(fileId, changes)).rejects.toThrow('Database error');
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('getVersionHistory', () => {
    it('should return version history for a file', async () => {
      const fileId = 'test-file-id';
      const mockVersions = [
        {
          id: 'version-1',
          file_id: fileId,
          version: '1.0.1',
          created_by: 'user-1',
          created_at: new Date(),
          changes: 'Second version',
          is_locked: false,
          is_released: false,
          file_path: 'files/test-file-id/1.0.1/test.e3'
        },
        {
          id: 'version-2',
          file_id: fileId,
          version: '1.0.0',
          created_by: 'user-1',
          created_at: new Date(),
          changes: 'Initial version',
          is_locked: true,
          is_released: true,
          file_path: 'files/test-file-id/1.0.0/test.e3'
        }
      ];

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: mockVersions });

      const result = await service.getVersionHistory(fileId);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(Version);
      expect(result[0]!.version).toBe('1.0.1');
      expect(result[1]!.version).toBe('1.0.0');
    });
  });

  describe('getVersion', () => {
    it('should return a specific version', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';
      const mockVersion = {
        id: 'version-1',
        file_id: fileId,
        version: version,
        created_by: 'user-1',
        created_at: new Date(),
        changes: 'Initial version',
        is_locked: false,
        is_released: false,
        file_path: 'files/test-file-id/1.0.0/test.e3'
      };

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: [mockVersion] });

      const result = await service.getVersion(fileId, version);

      expect(result).toBeInstanceOf(Version);
      expect(result?.version).toBe(version);
    });

    it('should return null if version not found', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: [] });

      const result = await service.getVersion(fileId, version);

      expect(result).toBeNull();
    });
  });

  describe('lockVersion', () => {
    it('should lock a version successfully', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock).mockResolvedValue({ rowCount: 1 });

      await expect(service.lockVersion(fileId, version)).resolves.not.toThrow();
    });

    it('should throw error if version not found or already locked', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock).mockResolvedValue({ rowCount: 0 });

      await expect(service.lockVersion(fileId, version))
        .rejects.toThrow('Version 1.0.0 of file test-file-id not found or already locked');
    });
  });

  describe('releaseVersion', () => {
    it('should release a version successfully', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock).mockResolvedValue({ rowCount: 1 });

      await expect(service.releaseVersion(fileId, version)).resolves.not.toThrow();
    });

    it('should throw error if version not found or already released', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock).mockResolvedValue({ rowCount: 0 });

      await expect(service.releaseVersion(fileId, version))
        .rejects.toThrow('Version 1.0.0 of file test-file-id not found or already released');
    });
  });

  describe('compareVersions', () => {
    it('should compare two versions successfully', async () => {
      const fileId = 'test-file-id';
      const version1 = '1.0.0';
      const version2 = '1.0.1';

      const mockVersion1 = {
        id: 'version-1',
        file_id: fileId,
        version: version1,
        created_by: 'user-1',
        created_at: new Date('2023-01-01'),
        changes: 'Initial version',
        is_locked: true,
        is_released: true,
        file_path: 'files/test-file-id/1.0.0/test.e3'
      };

      const mockVersion2 = {
        id: 'version-2',
        file_id: fileId,
        version: version2,
        created_by: 'user-1',
        created_at: new Date('2023-01-02'),
        changes: 'Second version',
        is_locked: false,
        is_released: false,
        file_path: 'files/test-file-id/1.0.1/test.e3'
      };

      (mockDb.query as jest.Mock)
        .mockResolvedValueOnce({ rows: [mockVersion1] })
        .mockResolvedValueOnce({ rows: [mockVersion2] });

      const result = await service.compareVersions(fileId, version1, version2);

      expect(result).toHaveProperty('added');
      expect(result).toHaveProperty('removed');
      expect(result).toHaveProperty('modified');
      expect(result).toHaveProperty('details');
      expect(result.details['version1'].version).toBe(version1);
      expect(result.details['version2'].version).toBe(version2);
    });
  });

  describe('canDeleteVersion', () => {
    it('should allow deletion of non-released, non-current version', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock)
        .mockResolvedValueOnce({ rows: [{ count: '2' }] }) // Version count
        .mockResolvedValueOnce({ rows: [{ is_released: false }] }) // Version info
        .mockResolvedValueOnce({ rows: [{ current_version: '1.0.1' }] }); // File current version

      const result = await service.canDeleteVersion(fileId, version);

      expect(result.canDelete).toBe(true);
    });

    it('should not allow deletion of the only version', async () => {
      const fileId = 'test-file-id';
      const version = '1.0.0';

      (mockDb.query as jest.Mock)
        .mockResolvedValueOnce({ rows: [{ count: '1' }] }); // Version count

      const result = await service.canDeleteVersion(fileId, version);

      expect(result.canDelete).toBe(false);
      expect(result.reason).toBe('Cannot delete the only version of a file');
    });
  });
});
