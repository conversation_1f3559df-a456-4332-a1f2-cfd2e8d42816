import { Request, Response, NextFunction } from 'express';
import { AuthenticationService } from '../services/AuthenticationService';
import { AuthorizationService, Permission, Resource } from '../services/AuthorizationService';
import { User } from '../types';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

export interface AuthMiddlewareOptions {
  resource?: Resource;
  permission?: Permission;
  resourceIdParam?: string; // Parameter name to extract resource ID from request
}

export class AuthMiddleware {
  private authService: AuthenticationService;
  private authzService: AuthorizationService;

  constructor(authService: AuthenticationService, authzService: AuthorizationService) {
    this.authService = authService;
    this.authzService = authzService;
  }

  /**
   * Middleware to authenticate user from JWT token
   */
  authenticate() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return res.status(401).json({
            error: {
              code: 'UNAUTHORIZED',
              message: 'Missing or invalid authorization header',
              timestamp: new Date().toISOString()
            }
          });
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        
        try {
          const user = await this.authService.validateToken(token);
          req.user = user;
          return next();
        } catch (error) {
          return res.status(401).json({
            error: {
              code: 'INVALID_TOKEN',
              message: 'Invalid or expired token',
              timestamp: new Date().toISOString()
            }
          });
        }
      } catch (error) {
        return res.status(500).json({
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Authentication error',
            timestamp: new Date().toISOString()
          }
        });
      }
    };
  }

  /**
   * Middleware to authorize user for specific resource and permission
   */
  authorize(options: AuthMiddlewareOptions = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.user) {
          return res.status(401).json({
            error: {
              code: 'UNAUTHORIZED',
              message: 'User not authenticated',
              timestamp: new Date().toISOString()
            }
          });
        }

        const { resource, permission, resourceIdParam } = options;
        
        // If no specific authorization required, just check authentication
        if (!resource || !permission) {
          return next();
        }

        // Extract resource ID from request parameters if specified
        let resourceId: string | undefined;
        if (resourceIdParam) {
          resourceId = (req.params as any)[resourceIdParam] || (req.body as any)[resourceIdParam] || (req.query as any)[resourceIdParam] as string;
        }

        const hasPermission = await this.authzService.hasPermission(
          req.user,
          resource,
          permission,
          resourceId
        );

        if (!hasPermission) {
          return res.status(403).json({
            error: {
              code: 'FORBIDDEN',
              message: `Insufficient permissions for ${permission} access to ${resource}`,
              timestamp: new Date().toISOString()
            }
          });
        }

        return next();
      } catch (error) {
        return res.status(500).json({
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Authorization error',
            timestamp: new Date().toISOString()
          }
        });
      }
    };
  }

  /**
   * Middleware to require admin role
   */
  requireAdmin() {
    return this.authorize({
      resource: 'system',
      permission: 'admin'
    });
  }

  /**
   * Middleware to require project access
   */
  requireProjectAccess(permission: Permission = 'read') {
    return this.authorize({
      resource: 'project',
      permission,
      resourceIdParam: 'projectId'
    });
  }

  /**
   * Middleware to require file access
   */
  requireFileAccess(permission: Permission = 'read') {
    return this.authorize({
      resource: 'file',
      permission,
      resourceIdParam: 'fileId'
    });
  }

  /**
   * Middleware to check if user can access their own data or is admin
   */
  requireSelfOrAdmin() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.user) {
          return res.status(401).json({
            error: {
              code: 'UNAUTHORIZED',
              message: 'User not authenticated',
              timestamp: new Date().toISOString()
            }
          });
        }

        const targetUserId = (req.params as any).userId || (req.body as any).userId;
        
        // Allow if user is accessing their own data or is admin
        if (req.user.id === targetUserId || req.user.role === 'admin') {
          return next();
        }

        return res.status(403).json({
          error: {
            code: 'FORBIDDEN',
            message: 'Can only access your own data unless you are an admin',
            timestamp: new Date().toISOString()
          }
        });
      } catch (error) {
        return res.status(500).json({
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Authorization error',
            timestamp: new Date().toISOString()
          }
        });
      }
    };
  }
}

/**
 * Helper function to create auth middleware with services
 */
export function createAuthMiddleware(
  authService: AuthenticationService,
  authzService: AuthorizationService
): AuthMiddleware {
  return new AuthMiddleware(authService, authzService);
}

/**
 * Simple authentication middleware function
 * This is a basic implementation that should be replaced with proper service injection
 */
export const auth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header',
          timestamp: new Date().toISOString()
        }
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // TODO: This should use proper service injection
    // For now, we'll create a mock user for development
    // In production, this should validate the JWT token
    if (token === 'mock-token') {
      req.user = {
        id: 'mock-user-id',
        username: 'mockuser',
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date()
      };
      return next();
    }

    return res.status(401).json({
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Authentication error',
        timestamp: new Date().toISOString()
      }
    });
  }
};