"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProjectRoutes = createProjectRoutes;
const express_1 = require("express");
const ProjectManagementService_1 = require("../services/ProjectManagementService");
const ProjectHierarchyService_1 = require("../services/ProjectHierarchyService");
const ProjectMetadataService_1 = require("../services/ProjectMetadataService");
const ProjectArchivalService_1 = require("../services/ProjectArchivalService");
const auth_1 = require("../middleware/auth");
function createProjectRoutes(db) {
    const router = (0, express_1.Router)();
    const projectService = new ProjectManagementService_1.ProjectManagementService(db);
    const hierarchyService = new ProjectHierarchyService_1.ProjectHierarchyService(db);
    const metadataService = new ProjectMetadataService_1.ProjectMetadataService(db);
    const archivalService = new ProjectArchivalService_1.ProjectArchivalService(db);
    router.get('/', auth_1.auth, async (req, res) => {
        try {
            const user = req.user;
            const projects = await projectService.listProjects(user.id);
            res.json({
                projects: projects.map(project => ({
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }))
            });
        }
        catch (error) {
            console.error('List projects error:', error);
            res.status(500).json({
                error: {
                    code: 'LIST_PROJECTS_FAILED',
                    message: 'Failed to list projects',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/', auth_1.auth, async (req, res) => {
        try {
            const user = req.user;
            const projectData = req.body;
            if (!projectData.name || projectData.name.trim().length === 0) {
                return res.status(400).json({
                    error: {
                        code: 'INVALID_PROJECT_DATA',
                        message: 'Project name is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const project = await projectService.createProject(projectData, user.id);
            res.status(201).json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }
            });
        }
        catch (error) {
            console.error('Create project error:', error);
            res.status(500).json({
                error: {
                    code: 'CREATE_PROJECT_FAILED',
                    message: 'Failed to create project',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const project = await projectService.getProject(projectId, user.id);
            if (!project) {
                return res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            res.json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata,
                    permissions: project.permissions
                }
            });
        }
        catch (error) {
            console.error('Get project error:', error);
            res.status(500).json({
                error: {
                    code: 'GET_PROJECT_FAILED',
                    message: 'Failed to get project',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.put('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const updates = req.body;
            const project = await projectService.updateProject(projectId, updates, user.id);
            res.json({
                project: {
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    status: project.status,
                    createdAt: project.createdAt,
                    updatedAt: project.updatedAt,
                    createdBy: project.createdBy,
                    metadata: project.metadata
                }
            });
        }
        catch (error) {
            console.error('Update project error:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            else {
                res.status(500).json({
                    error: {
                        code: 'UPDATE_PROJECT_FAILED',
                        message: 'Failed to update project',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
    });
    router.delete('/:projectId', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            await projectService.archiveProject(projectId, user.id);
            res.json({
                message: 'Project archived successfully'
            });
        }
        catch (error) {
            console.error('Archive project error:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({
                    error: {
                        code: 'PROJECT_NOT_FOUND',
                        message: 'Project not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            else {
                res.status(500).json({
                    error: {
                        code: 'ARCHIVE_PROJECT_FAILED',
                        message: 'Failed to archive project',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
    });
    router.get('/:projectId/hierarchy', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const hierarchy = await hierarchyService.getProjectHierarchy(projectId, user.id);
            res.json({
                hierarchy
            });
        }
        catch (error) {
            console.error('Get hierarchy error:', error);
            res.status(500).json({
                error: {
                    code: 'GET_HIERARCHY_FAILED',
                    message: 'Failed to get project hierarchy',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/:projectId/folders', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const { name, parentId } = req.body;
            if (!name || name.trim().length === 0) {
                return res.status(400).json({
                    error: {
                        code: 'INVALID_FOLDER_DATA',
                        message: 'Folder name is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const folder = await hierarchyService.createFolder(projectId, name, user.id, parentId);
            res.status(201).json({
                folder: {
                    id: folder.id,
                    name: folder.name,
                    path: folder.path,
                    parentId: folder.parentId,
                    createdAt: folder.createdAt,
                    createdBy: folder.createdBy
                }
            });
        }
        catch (error) {
            console.error('Create folder error:', error);
            res.status(500).json({
                error: {
                    code: 'CREATE_FOLDER_FAILED',
                    message: 'Failed to create folder',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.put('/:projectId/metadata', auth_1.auth, async (req, res) => {
        try {
            const { projectId } = req.params;
            const user = req.user;
            const { metadata } = req.body;
            await metadataService.updateProjectMetadata(projectId, metadata, user.id);
            res.json({
                message: 'Project metadata updated successfully'
            });
        }
        catch (error) {
            console.error('Update metadata error:', error);
            res.status(500).json({
                error: {
                    code: 'UPDATE_METADATA_FAILED',
                    message: 'Failed to update project metadata',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    return router;
}
//# sourceMappingURL=projectRoutes.js.map