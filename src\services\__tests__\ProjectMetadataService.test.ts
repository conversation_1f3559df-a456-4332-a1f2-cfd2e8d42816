// Mock dependencies before importing
const mockQuery = jest.fn();
const mockConnect = jest.fn();
const mockRelease = jest.fn();
const mockBegin = jest.fn();
const mockCommit = jest.fn();
const mockRollback = jest.fn();

const mockClient = {
  query: mockQuery,
  release: mockRelease
};

const mockPool = {
  query: mockQuery,
  connect: jest.fn().mockResolvedValue(mockClient)
};

const mockAuthService = {
  hasPermission: jest.fn()
};

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-change-uuid')
  },
  writable: true
});

import { ProjectMetadataService } from '../ProjectMetadataService';
import { ProjectManagementError } from '../ProjectManagementService';

describe('ProjectMetadataService', () => {
  let service: ProjectMetadataService;

  beforeEach(() => {
    service = new ProjectMetadataService(mockPool as any, mockAuthService as any);
    jest.clearAllMocks();
    
    // Setup default mock behaviors
    mockClient.query.mockImplementation(mockQuery);
    mockConnect.mockResolvedValue(mockClient);
    mockBegin.mockResolvedValue(undefined);
    mockCommit.mockResolvedValue(undefined);
    mockRollback.mockResolvedValue(undefined);
    mockAuthService.hasPermission.mockResolvedValue(true);
  });

  describe('validateMetadata', () => {
    it('should validate correct metadata', () => {
      const metadata = {
        projectType: 'electrical',
        priority: 'high',
        budget: 50000,
        deadline: new Date(),
        tags: ['important', 'urgent'],
        version: '1.0.0',
        department: 'Engineering',
        isConfidential: true
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid project type', () => {
      const metadata = {
        projectType: 'invalid-type'
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        "Field 'projectType' must be one of: electrical, mechanical, software, mixed"
      );
    });

    it('should reject invalid priority', () => {
      const metadata = {
        priority: 'super-urgent'
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        "Field 'priority' must be one of: low, medium, high, critical"
      );
    });

    it('should reject negative budget', () => {
      const metadata = {
        budget: -1000
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'budget' must be at least 0");
    });

    it('should reject invalid version format', () => {
      const metadata = {
        version: 'invalid-version'
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'version' does not match required pattern");
    });

    it('should reject short department name', () => {
      const metadata = {
        department: 'A'
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'department' must be at least 2 characters long");
    });

    it('should reject long department name', () => {
      const metadata = {
        department: 'A'.repeat(51)
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'department' must be no more than 50 characters long");
    });

    it('should validate type checking', () => {
      const metadata = {
        projectType: 123, // Should be string
        budget: 'not-a-number', // Should be number
        isConfidential: 'yes', // Should be boolean
        tags: 'not-an-array', // Should be array
        customFields: 'not-an-object' // Should be object
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'projectType' must be of type string");
      expect(result.errors).toContain("Field 'budget' must be of type number");
      expect(result.errors).toContain("Field 'isConfidential' must be of type boolean");
      expect(result.errors).toContain("Field 'tags' must be of type array");
      expect(result.errors).toContain("Field 'customFields' must be of type object");
    });

    it('should handle date validation', () => {
      const metadata = {
        deadline: '2024-12-31' // Valid date string
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(true);
    });

    it('should reject invalid date', () => {
      const metadata = {
        deadline: 'invalid-date'
      };

      const result = service.validateMetadata(metadata);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'deadline' must be of type date");
    });
  });

  describe('updateMetadata', () => {
    const projectId = 'project-123';
    const updatedBy = 'user-123';

    it('should update metadata successfully', async () => {
      const currentMetadata = {
        projectType: 'electrical',
        priority: 'medium'
      };

      const updates = {
        priority: 'high',
        budget: 75000
      };

      mockQuery
        .mockResolvedValueOnce({ // get current metadata
          rows: [{ metadata: currentMetadata }]
        })
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: projectId }] }) // UPDATE project
        .mockResolvedValueOnce({ rows: [{ id: 'change-1' }] }) // INSERT change for priority
        .mockResolvedValueOnce({ rows: [{ id: 'change-2' }] }) // INSERT change for budget
        .mockResolvedValueOnce(undefined); // COMMIT

      await service.updateMetadata(projectId, updates, updatedBy, 'Updating project priorities');

      expect(mockQuery).toHaveBeenCalledWith(
        'UPDATE projects SET metadata = $1, updated_at = $2 WHERE id = $3',
        [
          JSON.stringify({ projectType: 'electrical', priority: 'high', budget: 75000 }),
          expect.any(Date),
          projectId
        ]
      );

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO project_metadata_changes'),
        expect.arrayContaining([
          'test-change-uuid',
          projectId,
          'priority',
          JSON.stringify('medium'),
          JSON.stringify('high'),
          updatedBy,
          expect.any(Date),
          'Updating project priorities'
        ])
      );
    });

    it('should reject update without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.updateMetadata(projectId, { priority: 'high' }, updatedBy))
        .rejects.toThrow(ProjectManagementError);

      expect(mockAuthService.hasPermission).toHaveBeenCalledWith(
        { id: updatedBy },
        'project',
        'write',
        projectId
      );
    });

    it('should handle project not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await expect(service.updateMetadata(projectId, { priority: 'high' }, updatedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject invalid metadata', async () => {
      const currentMetadata = {};
      const invalidUpdates = {
        projectType: 'invalid-type'
      };

      mockQuery.mockResolvedValueOnce({
        rows: [{ metadata: currentMetadata }]
      });

      await expect(service.updateMetadata(projectId, invalidUpdates, updatedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should not record changes for unchanged values', async () => {
      const currentMetadata = {
        priority: 'high'
      };

      const updates = {
        priority: 'high' // Same value
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [{ metadata: currentMetadata }] })
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: projectId }] }) // UPDATE project
        .mockResolvedValueOnce(undefined); // COMMIT

      await service.updateMetadata(projectId, updates, updatedBy);

      // Should not insert any change records
      expect(mockQuery).not.toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO project_metadata_changes'),
        expect.any(Array)
      );
    });

    it('should rollback on error', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [{ metadata: {} }] })
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')); // UPDATE fails

      await expect(service.updateMetadata(projectId, { priority: 'high' }, updatedBy))
        .rejects.toThrow(ProjectManagementError);

      expect(mockQuery).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('getMetadataHistory', () => {
    const projectId = 'project-123';
    const userId = 'user-123';

    it('should get metadata history successfully', async () => {
      const mockChanges = [
        {
          id: 'change-1',
          project_id: projectId,
          field: 'priority',
          old_value: JSON.stringify('medium'),
          new_value: JSON.stringify('high'),
          changed_by: userId,
          changed_at: new Date(),
          reason: 'Increased priority'
        },
        {
          id: 'change-2',
          project_id: projectId,
          field: 'budget',
          old_value: JSON.stringify(50000),
          new_value: JSON.stringify(75000),
          changed_by: userId,
          changed_at: new Date(),
          reason: 'Budget adjustment'
        }
      ];

      mockQuery.mockResolvedValueOnce({ rows: mockChanges });

      const result = await service.getMetadataHistory(projectId, userId);

      expect(result).toHaveLength(2);
      expect(result[0]?.field).toBe('priority');
      expect(result[0]?.oldValue).toBe('medium');
      expect(result[0]?.newValue).toBe('high');
      expect(result[1]?.field).toBe('budget');
      expect(result[1]?.oldValue).toBe(50000);
      expect(result[1]?.newValue).toBe(75000);
    });

    it('should reject access without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.getMetadataHistory(projectId, userId))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle empty history', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await service.getMetadataHistory(projectId, userId);

      expect(result).toHaveLength(0);
    });

    it('should respect limit parameter', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await service.getMetadataHistory(projectId, userId, 25);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('LIMIT $2'),
        [projectId, 25]
      );
    });
  });

  describe('revertMetadataField', () => {
    const projectId = 'project-123';
    const field = 'priority';
    const changeId = 'change-123';
    const revertedBy = 'user-123';

    beforeEach(() => {
      mockAuthService.hasPermission.mockResolvedValue(true);
    });

    it('should revert metadata field successfully', async () => {
      const mockChange = {
        id: changeId,
        project_id: projectId,
        field: 'priority',
        old_value: JSON.stringify('medium'),
        new_value: JSON.stringify('high')
      };

      // Mock the change record query
      mockQuery.mockResolvedValueOnce({ rows: [mockChange] });

      // Mock the updateMetadata call (we need to mock the internal calls)
      mockQuery
        .mockResolvedValueOnce({ rows: [{ metadata: { priority: 'high' } }] }) // get current metadata
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: projectId }] }) // UPDATE project
        .mockResolvedValueOnce({ rows: [{ id: 'revert-change' }] }) // INSERT change record
        .mockResolvedValueOnce(undefined); // COMMIT

      await service.revertMetadataField(projectId, field, changeId, revertedBy, 'Reverting change');

      expect(mockQuery).toHaveBeenCalledWith(
        'SELECT * FROM project_metadata_changes WHERE id = $1 AND project_id = $2',
        [changeId, projectId]
      );
    });

    it('should reject revert without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.revertMetadataField(projectId, field, changeId, revertedBy))
        .rejects.toThrow(ProjectManagementError);

      expect(mockAuthService.hasPermission).toHaveBeenCalledWith(
        { id: revertedBy },
        'project',
        'admin',
        projectId
      );
    });

    it('should handle change record not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await expect(service.revertMetadataField(projectId, field, changeId, revertedBy))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('Custom Validation Rules', () => {
    it('should add and use custom validation rules', () => {
      const customRules = [
        {
          field: 'customField',
          type: 'string' as const,
          required: true,
          minLength: 5
        }
      ];

      service.addValidationRules('custom', customRules);

      const metadata = {
        customField: 'abc' // Too short
      };

      const result = service.validateMetadata(metadata, 'custom');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Field 'customField' must be at least 5 characters long");
    });

    it('should use custom validator function', () => {
      const customRules = [
        {
          field: 'email',
          type: 'string' as const,
          customValidator: (value: string) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return {
              isValid: emailRegex.test(value),
              error: 'Invalid email format'
            };
          }
        }
      ];

      service.addValidationRules('email-project', customRules);

      const invalidMetadata = { email: 'invalid-email' };
      const validMetadata = { email: '<EMAIL>' };

      const invalidResult = service.validateMetadata(invalidMetadata, 'email-project');
      const validResult = service.validateMetadata(validMetadata, 'email-project');

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Invalid email format');

      expect(validResult.isValid).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      mockConnect.mockRejectedValueOnce(new Error('Connection failed'));

      await expect(service.updateMetadata('project-123', { priority: 'high' }, 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle JSON parsing errors in history', async () => {
      const mockChanges = [
        {
          id: 'change-1',
          project_id: 'project-123',
          field: 'priority',
          old_value: 'invalid-json{',
          new_value: JSON.stringify('high'),
          changed_by: 'user-123',
          changed_at: new Date(),
          reason: null
        }
      ];

      mockQuery.mockResolvedValueOnce({ rows: mockChanges });

      await expect(service.getMetadataHistory('project-123', 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });
  });
});
