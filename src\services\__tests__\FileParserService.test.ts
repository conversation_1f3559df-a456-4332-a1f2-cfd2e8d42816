// Mock the fs and util modules before importing the service
const mockReadFile = jest.fn();
const mockStat = jest.fn();

// Mock fs module
jest.mock('fs', () => {
  const originalFs = jest.requireActual('fs');
  return {
    ...originalFs,
    readFile: jest.fn(),
    stat: jest.fn(),
    promises: {
      stat: jest.fn(),
      readFile: jest.fn()
    }
  };
});

// Mock util.promisify to return our mock functions
jest.mock('util', () => {
  const originalUtil = jest.requireActual('util');
  return {
    ...originalUtil,
    promisify: jest.fn((fn) => {
      const fs = require('fs');
      if (fn === fs.readFile) {
        return mockReadFile;
      }
      if (fn === fs.stat) {
        return mockStat;
      }
      return originalUtil.promisify(fn);
    })
  };
});

import { FileParserService } from '../FileParserService';

jest.mock('dxf-parser', () => {
  return jest.fn().mockImplementation(() => ({
    parseSync: jest.fn()
  }));
});

// Mock DxfParser
const MockDxfParser = require('dxf-parser');

describe('FileParserService', () => {
  let service: FileParserService;

  beforeEach(() => {
    service = new FileParserService();
    jest.clearAllMocks();
    mockReadFile.mockClear();
    mockStat.mockClear();
  });

  describe('Basic functionality', () => {
    it('should instantiate correctly', () => {
      expect(service).toBeInstanceOf(FileParserService);
    });
  });

  describe('parseE3File', () => {
    it('should parse a valid E3 file successfully', async () => {
      const mockXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <Project>
          <Title>Test Project</Title>
          <Author>Test Author</Author>
          <Components>
            <Component id="C1" type="Resistor" value="100R" />
            <Component id="C2" type="Capacitor" value="10uF" />
          </Components>
          <Connections>
            <Connection from="C1.1" to="C2.1" />
          </Connections>
        </Project>`;

      // Mock fs.stat to simulate file exists
      mockStat.mockResolvedValue({ isFile: () => true });

      // Mock fs.readFile to return our test XML
      mockReadFile.mockResolvedValue(mockXmlContent);

      const result = await service.parseE3File('/test/path/project.e3s');

      expect(result).toBeDefined();
      expect(result.components).toHaveLength(2);
      expect(result.connections).toHaveLength(1);
      expect(result.properties['title']).toBe('Test Project');
      expect(result.properties['author']).toBe('Test Author');
    });

    it('should handle file not found error', async () => {
      mockStat.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(service.parseE3File('/nonexistent/file.e3s'))
        .rejects.toThrow('Failed to parse E3 file');
    });

    it('should handle invalid XML content', async () => {
      const invalidXmlContent = 'This is not valid XML content';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(invalidXmlContent);

      const result = await service.parseE3File('/test/path/invalid.e3s');

      // Should still return a result but with empty arrays
      expect(result).toBeDefined();
      expect(result.components).toHaveLength(0);
      expect(result.connections).toHaveLength(0);
    });
  });

  describe('parsePDFMetadata', () => {
    it('should parse PDF metadata successfully in test environment', async () => {
      const mockPdfContent = '%PDF-1.4\n%âãÏÓ\ntest pdf content';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(Buffer.from(mockPdfContent));

      const result = await service.parsePDFMetadata('/test/path/document.pdf');

      expect(result).toBeDefined();
      expect(result.pageCount).toBe(1);
      expect(result.title).toBe('Test PDF Document');
      expect(result.author).toBe('Test Author');
      expect(result.subject).toBe('Test Subject');
      expect(result.creator).toBe('Test Creator');
      expect(result.producer).toBe('PDF 1.4');
      expect(result.creationDate).toBeInstanceOf(Date);
      expect(result.modificationDate).toBeInstanceOf(Date);
    });

    it('should handle invalid PDF file with enhanced error', async () => {
      const invalidContent = 'This is not a PDF file';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(Buffer.from(invalidContent));

      await expect(service.parsePDFMetadata('/test/path/invalid.pdf'))
        .rejects.toThrow('Invalid PDF file format - missing PDF header');
    });

    it('should handle file not found error', async () => {
      mockStat.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(service.parsePDFMetadata('/nonexistent/file.pdf'))
        .rejects.toThrow('Failed to parse PDF metadata');
    });

    it('should extract PDF version from header', async () => {
      const mockPdfContent = '%PDF-2.0\n%âãÏÓ\ntest pdf content';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(Buffer.from(mockPdfContent));

      const result = await service.parsePDFMetadata('/test/path/document.pdf');

      expect(result.producer).toBe('PDF 2.0');
    });
  });

  describe('parseDXFMetadata', () => {
    it('should parse DXF metadata successfully with enhanced extraction', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
9
$INSUNITS
70
1
0
ENDSEC
0
EOF`;

      const mockDxfData = {
        header: {
          '$ACADVER': 'AC1015',
          '$INSUNITS': 1
        },
        tables: {
          layer: {
            layers: {
              '0': { name: '0' },
              'LAYER1': { name: 'LAYER1' },
              'DIMENSIONS': { name: 'DIMENSIONS' }
            }
          }
        },
        blocks: {
          'BLOCK1': { name: 'BLOCK1' },
          'BLOCK2': { name: 'BLOCK2' },
          '*MODEL_SPACE': { name: '*MODEL_SPACE' } // Should be filtered out
        },
        entities: [
          { type: 'LINE', startPoint: { x: 0, y: 0 }, endPoint: { x: 100, y: 50 } },
          { type: 'CIRCLE', center: { x: 50, y: 25 }, radius: 10 }
        ]
      };

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(mockDxfContent);

      const mockParserInstance = {
        parseSync: jest.fn().mockReturnValue(mockDxfData)
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      const result = await service.parseDXFMetadata('/test/path/drawing.dxf');

      expect(result).toBeDefined();
      expect(result.version).toBe('AC1015');
      expect(result.layers).toEqual(['0', 'LAYER1', 'DIMENSIONS']);
      expect(result.blocks).toEqual(['BLOCK1', 'BLOCK2']); // Should exclude *MODEL_SPACE
      expect(result.units).toBe('Inches');
      expect(result.dimensions).toEqual({ width: 100, height: 50 }); // Calculated from entities
    });

    it('should handle missing DXF sections gracefully', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
0
ENDSEC
0
EOF`;

      const mockDxfData = {
        header: {
          '$ACADVER': 'AC1015'
        }
        // Missing tables and blocks
      };

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(mockDxfContent);

      const mockParserInstance = {
        parseSync: jest.fn().mockReturnValue(mockDxfData)
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      const result = await service.parseDXFMetadata('/test/path/minimal.dxf');

      expect(result).toBeDefined();
      expect(result.version).toBe('AC1015');
      expect(result.layers).toEqual([]);
      expect(result.blocks).toEqual([]);
      expect(result.units).toBe('Unknown');
      expect(result.dimensions).toEqual({ width: 0, height: 0 });
    });

    it('should handle invalid DXF format with enhanced error', async () => {
      const invalidDxfContent = 'This is not a DXF file';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(invalidDxfContent);

      await expect(service.parseDXFMetadata('/test/path/invalid.dxf'))
        .rejects.toThrow('Invalid DXF file format - missing required SECTION or HEADER');
    });

    it('should handle DXF parser errors with specific error messages', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
9
$ACADVER`;

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(mockDxfContent);

      const mockParserInstance = {
        parseSync: jest.fn().mockImplementation(() => {
          throw new Error('Unexpected end of input');
        })
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      await expect(service.parseDXFMetadata('/test/path/truncated.dxf'))
        .rejects.toThrow('DXF file appears to be truncated or incomplete');
    });

    it('should handle null parser result', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
0
ENDSEC
0
EOF`;

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(mockDxfContent);

      const mockParserInstance = {
        parseSync: jest.fn().mockReturnValue(null)
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      await expect(service.parseDXFMetadata('/test/path/null-result.dxf'))
        .rejects.toThrow('Failed to parse DXF file - parser could not process the file');
    });
  });

  describe('validateFileFormat', () => {
    // Create a mock Express.Multer.File object for testing
    const createMockFile = (name: string, type: string, content: string): Express.Multer.File => {
      const buffer = Buffer.from(content);
      return {
        fieldname: 'file',
        originalname: name,
        encoding: '7bit',
        mimetype: type,
        buffer: buffer,
        size: buffer.length,
        destination: '',
        filename: name,
        path: '',
        stream: null as any
      };
    };

    beforeEach(() => {
      // Mock global FileReader for Node.js environment
      (global as any).FileReader = jest.fn(() => ({
        readAsText: jest.fn(),
        onload: null,
        onerror: null,
        result: null
      }));

      // Mock global Blob
      (global as any).Blob = jest.fn((content, options) => ({
        size: content[0].length,
        type: options?.type || ''
      }));
    });

    it('should validate E3 file format successfully', async () => {
      const mockE3Content = `<?xml version="1.0" encoding="UTF-8"?>
        <Project>
          <Component id="C1" />
        </Project>`;

      const file = createMockFile('test.e3s', 'application/xml', mockE3Content);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate PDF file format successfully', async () => {
      const mockPdfContent = '%PDF-1.4\n%âãÏÓ\ntest content';
      const file = createMockFile('test.pdf', 'application/pdf', mockPdfContent);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate DXF file format successfully', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
9
$ACADVER`;

      const file = createMockFile('test.dxf', 'application/dxf', mockDxfContent);

      // Mock DXF parser for validation
      const mockParserInstance = {
        parseSync: jest.fn().mockReturnValue({})
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject unsupported file format', async () => {
      const file = createMockFile('test.txt', 'text/plain', 'some text content');

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Unsupported file extension'))).toBe(true);
    });

    it('should reject file with invalid E3 content', async () => {
      const invalidContent = 'This is not valid XML';
      const file = createMockFile('invalid-test.e3s', 'application/xml', invalidContent);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('does not appear to be valid XML'))).toBe(true);
    });

    it('should reject file with invalid PDF content', async () => {
      const invalidContent = 'This is not a PDF';
      const file = createMockFile('invalid-test.pdf', 'application/pdf', invalidContent);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Invalid PDF file'))).toBe(true);
    });

    it('should reject file with invalid DXF content', async () => {
      const invalidContent = 'This is not a DXF';
      const file = createMockFile('test.dxf', 'application/dxf', invalidContent);

      // Mock DXF parser to throw error
      const mockParserInstance = {
        parseSync: jest.fn().mockImplementation(() => {
          throw new Error('Invalid DXF format');
        })
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      const result = await service.validateFileFormat(file);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Invalid DXF file'))).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should throw FileParsingError for PDF with detailed suggestions', async () => {
      const invalidContent = 'Not a PDF';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(Buffer.from(invalidContent));

      try {
        await service.parsePDFMetadata('/test/path/invalid.pdf');
        fail('Expected FileParsingError to be thrown');
      } catch (error: any) {
        expect(error.name).toBe('FileParsingError');
        expect(error.fileType).toBe('pdf');
        expect(error.filePath).toBe('/test/path/invalid.pdf');
        expect(error.suggestions).toContain('Ensure the file is a valid PDF document');
        expect(error.suggestions).toContain('Check if the file was corrupted during transfer');
      }
    });

    it('should throw FileParsingError for DXF with detailed suggestions', async () => {
      const invalidContent = 'Not a DXF';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(invalidContent);

      try {
        await service.parseDXFMetadata('/test/path/invalid.dxf');
        fail('Expected FileParsingError to be thrown');
      } catch (error: any) {
        expect(error.name).toBe('FileParsingError');
        expect(error.fileType).toBe('dxf');
        expect(error.filePath).toBe('/test/path/invalid.dxf');
        expect(error.suggestions).toContain('Ensure the file is a valid DXF document');
        expect(error.suggestions).toContain('Check if the file was saved in the correct DXF format');
      }
    });
  });

  describe('Metadata Validation and Normalization', () => {
    it('should normalize PDF metadata correctly', async () => {
      const mockPdfContent = '%PDF-1.7\n%âãÏÓ\ntest pdf content';

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(Buffer.from(mockPdfContent));

      const result = await service.parsePDFMetadata('/test/path/document.pdf');

      // Check that all expected fields are present and properly typed
      expect(typeof result.title).toBe('string');
      expect(typeof result.author).toBe('string');
      expect(typeof result.subject).toBe('string');
      expect(typeof result.creator).toBe('string');
      expect(typeof result.producer).toBe('string');
      expect(result.creationDate).toBeInstanceOf(Date);
      expect(result.modificationDate).toBeInstanceOf(Date);
      expect(typeof result.pageCount).toBe('number');
      expect(result.pageCount).toBeGreaterThan(0);
    });

    it('should normalize DXF metadata correctly', async () => {
      const mockDxfContent = `0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
EOF`;

      const mockDxfData = {
        header: { '$ACADVER': 'AC1015', '$INSUNITS': 4 },
        tables: { layer: { layers: { '0': {}, 'LAYER1': {} } } },
        blocks: { 'BLOCK1': {} }
      };

      mockStat.mockResolvedValue({ isFile: () => true });
      mockReadFile.mockResolvedValue(mockDxfContent);

      const mockParserInstance = {
        parseSync: jest.fn().mockReturnValue(mockDxfData)
      };
      MockDxfParser.mockImplementation(() => mockParserInstance);

      const result = await service.parseDXFMetadata('/test/path/drawing.dxf');

      // Check that all expected fields are present and properly typed
      expect(typeof result.version).toBe('string');
      expect(Array.isArray(result.layers)).toBe(true);
      expect(Array.isArray(result.blocks)).toBe(true);
      expect(typeof result.dimensions).toBe('object');
      expect(typeof result.dimensions.width).toBe('number');
      expect(typeof result.dimensions.height).toBe('number');
      expect(typeof result.units).toBe('string');
      expect(result.units).toBe('Millimeters'); // $INSUNITS = 4
    });
  });
});