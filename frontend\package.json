{"name": "e3-pdm-frontend", "version": "1.0.0", "description": "React frontend for E3 PDM System", "private": true, "dependencies": {"axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "react-scripts": "^5.0.1", "typescript": "^5.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}