import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { promisify } from 'util';
import { FileValidationResult } from '../types';

const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const unlink = promisify(fs.unlink);
const stat = promisify(fs.stat);
const access = promisify(fs.access);

export interface FileUploadData {
  buffer: Buffer;
  originalName: string;
  mimetype: string;
  size: number;
}

export interface StorageConfig {
  baseStoragePath: string;
  maxFileSize: number; // in bytes
  allowedMimeTypes: {
    'e3series': string[];
    'pdf': string[];
    'dxf': string[];
  };
}

export class FileStorageService {
  private config: StorageConfig;

  constructor(config?: Partial<StorageConfig>) {
    this.config = {
      baseStoragePath: config?.baseStoragePath || path.join(process.cwd(), 'storage'),
      maxFileSize: config?.maxFileSize || 1024 * 1024 * 1024, // 1GB default
      allowedMimeTypes: config?.allowedMimeTypes || {
        'e3series': ['application/octet-stream', 'application/x-e3series'],
        'pdf': ['application/pdf'],
        'dxf': ['application/dxf', 'image/vnd.dxf', 'application/octet-stream']
      }
    };
  }

  /**
   * Initialize storage directories
   */
  async initialize(): Promise<void> {
    try {
      await access(this.config.baseStoragePath);
    } catch {
      await mkdir(this.config.baseStoragePath, { recursive: true });
    }
  }

  /**
   * Store a file and return the file path
   */
  async storeFile(
    projectId: string, 
    fileData: FileUploadData, 
    fileId: string,
    version: string = '1.0.0'
  ): Promise<string> {
    // Create project directory if it doesn't exist
    const projectDir = path.join(this.config.baseStoragePath, projectId);
    try {
      await access(projectDir);
    } catch {
      await mkdir(projectDir, { recursive: true });
    }

    // Create version directory
    const versionDir = path.join(projectDir, fileId);
    try {
      await access(versionDir);
    } catch {
      await mkdir(versionDir, { recursive: true });
    }

    // Generate file path with version
    const fileExtension = path.extname(fileData.originalName);
    const fileName = `${version}${fileExtension}`;
    const filePath = path.join(versionDir, fileName);

    // Write file to disk
    await writeFile(filePath, fileData.buffer);

    return filePath;
  }

  /**
   * Get a readable stream for a file
   */
  getFileStream(filePath: string): NodeJS.ReadableStream {
    return fs.createReadStream(filePath);
  }

  /**
   * Retrieve a file as a buffer
   */
  async retrieveFile(filePath: string): Promise<Buffer> {
    try {
      await access(filePath);
      return await readFile(filePath);
    } catch (error) {
      throw new Error(`File not found or inaccessible: ${filePath}`);
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await access(filePath);
      await unlink(filePath);
    } catch (error) {
      throw new Error(`Failed to delete file: ${filePath}`);
    }
  }

  /**
   * Check if a file exists
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats
   */
  async getFileStats(filePath: string): Promise<fs.Stats> {
    try {
      return await stat(filePath);
    } catch (error) {
      throw new Error(`Failed to get file stats: ${filePath}`);
    }
  }

  /**
   * Calculate SHA-256 checksum of a file buffer
   */
  calculateChecksum(buffer: Buffer): string {
    return crypto.createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * Calculate SHA-256 checksum of a file from file path
   */
  async calculateChecksumFromFile(filePath: string): Promise<string> {
    const buffer = await this.retrieveFile(filePath);
    return this.calculateChecksum(buffer);
  }

  /**
   * Validate file format based on file type and content
   */
  validateFileFormat(fileData: FileUploadData, expectedType: 'e3series' | 'pdf' | 'dxf'): FileValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (fileData.size > this.config.maxFileSize) {
      errors.push(`File size (${fileData.size} bytes) exceeds maximum allowed size (${this.config.maxFileSize} bytes)`);
    }

    if (fileData.size === 0) {
      errors.push('File is empty');
    }

    // Check MIME type
    const allowedMimeTypes = this.config.allowedMimeTypes[expectedType];
    if (!allowedMimeTypes.includes(fileData.mimetype)) {
      warnings.push(`MIME type '${fileData.mimetype}' may not be compatible with file type '${expectedType}'`);
    }

    // Check file extension
    const fileExtension = path.extname(fileData.originalName).toLowerCase();
    const expectedExtensions = this.getExpectedExtensions(expectedType);
    
    if (!expectedExtensions.includes(fileExtension)) {
      errors.push(`File extension '${fileExtension}' is not valid for file type '${expectedType}'. Expected: ${expectedExtensions.join(', ')}`);
    }

    // Validate file content based on type
    const contentValidation = this.validateFileContent(fileData.buffer, expectedType);
    errors.push(...contentValidation.errors);
    warnings.push(...contentValidation.warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get expected file extensions for a file type
   */
  private getExpectedExtensions(fileType: 'e3series' | 'pdf' | 'dxf'): string[] {
    const extensions = {
      'e3series': ['.e3s', '.e3p', '.e3d'],
      'pdf': ['.pdf'],
      'dxf': ['.dxf']
    };
    return extensions[fileType];
  }

  /**
   * Validate file content based on file type
   */
  private validateFileContent(buffer: Buffer, fileType: 'e3series' | 'pdf' | 'dxf'): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (buffer.length === 0) {
      errors.push('File content is empty');
      return { errors, warnings };
    }

    switch (fileType) {
      case 'pdf':
        if (!buffer.subarray(0, 4).toString().startsWith('%PDF')) {
          errors.push('File does not appear to be a valid PDF (missing PDF header)');
        }
        break;

      case 'dxf':
        const dxfHeader = buffer.subarray(0, 100).toString('utf8');
        if (!dxfHeader.includes('SECTION') || !dxfHeader.includes('HEADER')) {
          errors.push('File does not appear to be a valid DXF (missing DXF structure)');
        }
        break;

      case 'e3series':
        // E3 series files can have various formats, so we do basic checks
        if (buffer.length < 100) {
          warnings.push('E3 series file appears to be very small, please verify it is complete');
        }
        // Additional e3 series validation would require knowledge of the specific format
        break;
    }

    return { errors, warnings };
  }

  /**
   * Verify file integrity using checksum
   */
  async verifyFileIntegrity(filePath: string, expectedChecksum: string): Promise<boolean> {
    try {
      const buffer = await this.retrieveFile(filePath);
      const actualChecksum = this.calculateChecksum(buffer);
      return actualChecksum === expectedChecksum;
    } catch {
      return false;
    }
  }

  /**
   * Get storage path for a project
   */
  getProjectStoragePath(projectId: string): string {
    return path.join(this.config.baseStoragePath, projectId);
  }

  /**
   * Get storage path for a file
   */
  getFileStoragePath(projectId: string, fileId: string): string {
    return path.join(this.config.baseStoragePath, projectId, fileId);
  }

  /**
   * Clean up empty directories
   */
  async cleanupEmptyDirectories(dirPath: string): Promise<void> {
    try {
      const files = await promisify(fs.readdir)(dirPath);
      if (files.length === 0) {
        await promisify(fs.rmdir)(dirPath);
        
        // Recursively clean up parent directory if it becomes empty
        const parentDir = path.dirname(dirPath);
        if (parentDir !== this.config.baseStoragePath) {
          await this.cleanupEmptyDirectories(parentDir);
        }
      }
    } catch {
      // Directory doesn't exist or can't be removed, ignore
    }
  }
}