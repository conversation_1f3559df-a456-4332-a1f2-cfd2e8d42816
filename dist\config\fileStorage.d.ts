export interface FileStorageConfig {
    baseStoragePath: string;
    tempStoragePath: string;
    maxFileSize: number;
    maxTotalStorageSize: number;
    allowedFileTypes: {
        'e3series': {
            extensions: string[];
            mimeTypes: string[];
            maxSize: number;
        };
        'pdf': {
            extensions: string[];
            mimeTypes: string[];
            maxSize: number;
        };
        'dxf': {
            extensions: string[];
            mimeTypes: string[];
            maxSize: number;
        };
    };
    enableVirusScanning: boolean;
    quarantinePath: string;
    enableAutoCleanup: boolean;
    cleanupIntervalHours: number;
    retainDeletedFilesHours: number;
    enableBackup: boolean;
    backupPath: string;
    backupRetentionDays: number;
}
export declare const defaultFileStorageConfig: FileStorageConfig;
export declare function validateFileStorageConfig(config: FileStorageConfig): {
    isValid: boolean;
    errors: string[];
};
export declare function getFileTypeByExtension(extension: string, config: FileStorageConfig): keyof FileStorageConfig['allowedFileTypes'] | null;
export declare function isFileTypeAllowed(fileType: string, config: FileStorageConfig): boolean;
export declare function getMaxFileSizeForType(fileType: keyof FileStorageConfig['allowedFileTypes'], config: FileStorageConfig): number;
export declare function ensureDirectoriesExist(config: FileStorageConfig): Promise<void>;
//# sourceMappingURL=fileStorage.d.ts.map