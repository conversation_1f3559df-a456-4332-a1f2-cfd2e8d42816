import { Request, Response, NextFunction } from 'express';
import { AuthenticationService } from '../services/AuthenticationService';
import { AuthorizationService, Permission, Resource } from '../services/AuthorizationService';
import { User } from '../types';
declare global {
    namespace Express {
        interface Request {
            user?: User;
        }
    }
}
export interface AuthMiddlewareOptions {
    resource?: Resource;
    permission?: Permission;
    resourceIdParam?: string;
}
export declare class AuthMiddleware {
    private authService;
    private authzService;
    constructor(authService: AuthenticationService, authzService: AuthorizationService);
    authenticate(): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
    authorize(options?: AuthMiddlewareOptions): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
    requireAdmin(): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
    requireProjectAccess(permission?: Permission): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
    requireFileAccess(permission?: Permission): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
    requireSelfOrAdmin(): (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
}
export declare function createAuthMiddleware(authService: AuthenticationService, authzService: AuthorizationService): AuthMiddleware;
export declare const auth: (req: Request, res: Response, next: NextFunction) => Promise<void | Response<any, Record<string, any>>>;
//# sourceMappingURL=auth.d.ts.map