# Design Document

## Overview

The e3 PDM System is designed as a web-based application with a modular architecture that provides comprehensive product data management capabilities for electrical design projects. The system will handle e3 series design files, PDF documents, and DXF drawings through a unified interface while maintaining strict version control and access management.

The architecture follows a layered approach with clear separation between the presentation layer, business logic, data access, and file storage components. This design ensures scalability, maintainability, and extensibility for future file format support.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Interface]
        API_CLIENT[API Client]
    end
    
    subgraph "Application Layer"
        AUTH[Authentication Service]
        FILE_MGR[File Management Service]
        PROJECT_MGR[Project Management Service]
        VERSION_MGR[Version Control Service]
        PARSER[File Parser Service]
    end
    
    subgraph "Data Layer"
        DB[(Database)]
        FILE_STORE[(File Storage)]
        CACHE[(Cache)]
    end
    
    WEB --> AUTH
    API_CLIENT --> AUTH
    AUTH --> FILE_MGR
    AUTH --> PROJECT_MGR
    FILE_MGR --> VERSION_MGR
    FILE_MGR --> PARSER
    PROJECT_MGR --> VERSION_MGR
    FILE_MGR --> DB
    PROJECT_MGR --> DB
    VERSION_MGR --> DB
    FILE_MGR --> FILE_STORE
    FILE_MGR --> CACHE
```

### Technology Stack

- **Backend Framework**: Node.js with Express.js for RESTful API
- **Database**: PostgreSQL for metadata and project data
- **File Storage**: Local filesystem with configurable cloud storage support
- **Authentication**: JWT-based authentication with role-based access control
- **Frontend**: React.js with TypeScript for the web interface
- **File Processing**: Custom parsers for e3 series files, PDF.js for PDF handling, DXF parser library

## Components and Interfaces

### File Management Service

**Responsibilities:**
- Handle file upload, download, and deletion operations
- Coordinate with file parsers for metadata extraction
- Manage file storage and retrieval
- Enforce file type validation and size limits

**Key Methods:**
```typescript
interface FileManagementService {
  uploadFile(file: File, projectId: string, metadata: FileMetadata): Promise<FileRecord>
  downloadFile(fileId: string, version?: string): Promise<FileStream>
  deleteFile(fileId: string): Promise<void>
  listFiles(projectId: string, filters?: FileFilters): Promise<FileRecord[]>
  extractMetadata(file: File): Promise<FileMetadata>
}
```

### Project Management Service

**Responsibilities:**
- Create and manage project hierarchies
- Handle project-level metadata and properties
- Manage project permissions and access controls
- Coordinate project archival and restoration

**Key Methods:**
```typescript
interface ProjectManagementService {
  createProject(projectData: ProjectData): Promise<Project>
  updateProject(projectId: string, updates: Partial<ProjectData>): Promise<Project>
  getProject(projectId: string): Promise<Project>
  listProjects(userId: string): Promise<Project[]>
  archiveProject(projectId: string): Promise<void>
}
```

### Version Control Service

**Responsibilities:**
- Track file versions and changes
- Maintain version history and metadata
- Handle version comparison where applicable
- Manage version locking and release states

**Key Methods:**
```typescript
interface VersionControlService {
  createVersion(fileId: string, changes: VersionChanges): Promise<Version>
  getVersionHistory(fileId: string): Promise<Version[]>
  compareVersions(fileId: string, version1: string, version2: string): Promise<VersionDiff>
  lockVersion(fileId: string, version: string): Promise<void>
}
```

### File Parser Service

**Responsibilities:**
- Parse e3 series files and extract component/connection data
- Extract metadata from PDF and DXF files
- Validate file formats and integrity
- Provide structured data representation

**Key Methods:**
```typescript
interface FileParserService {
  parseE3File(filePath: string): Promise<E3ProjectData>
  parsePDFMetadata(filePath: string): Promise<PDFMetadata>
  parseDXFMetadata(filePath: string): Promise<DXFMetadata>
  validateFileFormat(file: File): Promise<FileValidationResult>
}
```

## Data Models

### Core Data Models

```typescript
interface Project {
  id: string
  name: string
  description: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
  status: 'active' | 'archived'
  metadata: Record<string, any>
  permissions: ProjectPermission[]
}

interface FileRecord {
  id: string
  projectId: string
  name: string
  originalName: string
  fileType: 'e3series' | 'pdf' | 'dxf'
  size: number
  checksum: string
  uploadedBy: string
  uploadedAt: Date
  currentVersion: string
  metadata: FileMetadata
  versions: Version[]
}

interface Version {
  id: string
  fileId: string
  version: string
  createdBy: string
  createdAt: Date
  changes: string
  isLocked: boolean
  isReleased: boolean
  filePath: string
}

interface E3ProjectData {
  components: Component[]
  connections: Connection[]
  properties: Record<string, any>
  layers: Layer[]
  sheets: Sheet[]
}

interface Component {
  id: string
  name: string
  type: string
  properties: Record<string, any>
  position: { x: number, y: number }
  connections: string[]
}

interface Connection {
  id: string
  fromComponent: string
  toComponent: string
  signal: string
  properties: Record<string, any>
}
```

### Database Schema

```sql
-- Projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_by UUID NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  metadata JSONB
);

-- Files table
CREATE TABLE files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id),
  name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_type VARCHAR(20) NOT NULL,
  size BIGINT NOT NULL,
  checksum VARCHAR(64) NOT NULL,
  uploaded_by UUID NOT NULL,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  current_version VARCHAR(20) NOT NULL,
  metadata JSONB
);

-- Versions table
CREATE TABLE versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  file_id UUID REFERENCES files(id),
  version VARCHAR(20) NOT NULL,
  created_by UUID NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  changes TEXT,
  is_locked BOOLEAN DEFAULT FALSE,
  is_released BOOLEAN DEFAULT FALSE,
  file_path VARCHAR(500) NOT NULL
);
```

## Error Handling

### Error Categories

1. **File Processing Errors**
   - Invalid file format
   - Corrupted file data
   - Unsupported e3 series version
   - File size exceeded

2. **Access Control Errors**
   - Insufficient permissions
   - Authentication failures
   - Project access denied

3. **Data Integrity Errors**
   - Version conflicts
   - Missing dependencies
   - Database constraint violations

### Error Response Format

```typescript
interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: Record<string, any>
    timestamp: string
  }
}
```

### Error Handling Strategy

- All errors will be logged with appropriate severity levels
- User-facing errors will provide clear, actionable messages
- System errors will be abstracted to prevent information disclosure
- Critical errors will trigger automated alerts
- File processing errors will include recovery suggestions where applicable

## Testing Strategy

### Unit Testing
- Test individual service methods with mocked dependencies
- Validate file parsing logic with sample files
- Test data model validation and constraints
- Cover error handling scenarios

### Integration Testing
- Test API endpoints with realistic payloads
- Validate database operations and transactions
- Test file upload/download workflows
- Verify authentication and authorization flows

### End-to-End Testing
- Test complete user workflows from UI to database
- Validate file processing pipelines
- Test project management operations
- Verify version control functionality

### Performance Testing
- Load testing for concurrent file uploads
- Database query performance optimization
- File storage and retrieval benchmarks
- Memory usage monitoring for large file processing

### Test Data Requirements
- Sample e3 series files of various versions
- Test PDF documents with different metadata
- DXF files with various layer configurations
- Mock user accounts with different permission levels