
import * as path from 'path';
import * as os from 'os';
import { promisify } from 'util';
import * as fs from 'fs';
import { Pool } from 'pg';
import { FileStorageService } from './FileStorageService';
import { FileUploadService } from './FileUploadService';
import { VersionControlService } from './VersionControlService';
import { FileParserService } from './FileParserService';
import { FileRecord, FileMetadata, FileFilters, VersionChanges } from '../types';
import { FileManagementService as IFileManagementService } from '../types/services';

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);

export class FileManagementService implements IFileManagementService {
  private fileStorageService: FileStorageService;
  private fileUploadService: FileUploadService;
  private versionControlService: VersionControlService;
  private fileParserService: FileParserService;
  private db: Pool;

  constructor(
    fileStorageService: FileStorageService,
    fileUploadService: FileUploadService,
    versionControlService: VersionControlService,
    fileParserService: FileParserService,
    db: Pool
  ) {
    this.fileStorageService = fileStorageService;
    this.fileUploadService = fileUploadService;
    this.versionControlService = versionControlService;
    this.fileParserService = fileParserService;
    this.db = db;
  }

  /**
   * Upload a file to the system
   */
  async uploadFile(
    file: Express.Multer.File,
    projectId: string,
    uploadedBy: string,
    fileType: 'e3series' | 'pdf' | 'dxf',
    metadata: FileMetadata = {}
  ): Promise<FileRecord> {
    const client = await this.db.connect();

    try {
      await client.query('BEGIN');

      // Validate inputs
      if (!projectId || projectId.trim().length === 0) {
        throw new Error('Project ID is required');
      }

      if (!uploadedBy || uploadedBy.trim().length === 0) {
        throw new Error('Uploaded by user ID is required');
      }

      // Verify project exists
      const projectResult = await client.query(
        'SELECT id FROM projects WHERE id = $1 AND status = $2',
        [projectId, 'active']
      );

      if (projectResult.rows.length === 0) {
        throw new Error(`Project ${projectId} not found or not active`);
      }

      // Extract metadata from file
      const extractedMetadata = await this.extractMetadata(file);
      const combinedMetadata = { ...extractedMetadata, ...metadata };

      // Process the upload and store the file
      const fileRecord = await this.fileUploadService.processUpload(
        file,
        projectId,
        uploadedBy,
        fileType,
        combinedMetadata
      );

      // Save file record to database
      const fileId = crypto.randomUUID();
      await client.query(
        `INSERT INTO files (id, project_id, name, original_name, file_type, size, checksum, uploaded_by, uploaded_at, current_version, metadata)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          fileId,
          projectId,
          fileRecord.name,
          fileRecord.originalName,
          fileType,
          fileRecord.size,
          fileRecord.checksum,
          uploadedBy,
          new Date(),
          '1.0.0',
          JSON.stringify(combinedMetadata)
        ]
      );

      // Create initial version
      const versionChanges: VersionChanges = {
        description: 'Initial upload',
        modifiedBy: uploadedBy
      };

      const version = await this.versionControlService.createVersion(fileId, versionChanges);

      // Update the file record with the database ID and version info
      const updatedFileRecord: FileRecord = {
        ...fileRecord,
        id: fileId,
        currentVersion: version.version,
        versions: [version]
      };

      await client.query('COMMIT');
      return updatedFileRecord;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Download a file from the system
   */
  async downloadFile(fileId: string, version?: string): Promise<NodeJS.ReadableStream> {
    if (!fileId || fileId.trim().length === 0) {
      throw new Error('File ID is required');
    }

    // Get file record from database
    const fileResult = await this.db.query(
      'SELECT * FROM files WHERE id = $1 AND is_deleted = false',
      [fileId]
    );

    if (fileResult.rows.length === 0) {
      throw new Error(`File ${fileId} not found`);
    }

    // const fileRecord = fileResult.rows[0]; // File exists, proceed

    // Get the specific version or latest version
    let versionRecord;
    if (version) {
      versionRecord = await this.versionControlService.getVersion(fileId, version);
      if (!versionRecord) {
        throw new Error(`Version ${version} of file ${fileId} not found`);
      }
    } else {
      versionRecord = await this.versionControlService.getLatestVersion(fileId);
      if (!versionRecord) {
        throw new Error(`No versions found for file ${fileId}`);
      }
    }

    // Get file stream from storage service
    return this.fileStorageService.getFileStream(versionRecord.filePath);
  }

  /**
   * Delete a file from the system (soft delete)
   */
  async deleteFile(fileId: string): Promise<void> {
    const client = await this.db.connect();

    try {
      await client.query('BEGIN');

      if (!fileId || fileId.trim().length === 0) {
        throw new Error('File ID is required');
      }

      // Check if file exists and is not already deleted
      const fileResult = await client.query(
        'SELECT id FROM files WHERE id = $1 AND is_deleted = false',
        [fileId]
      );

      if (fileResult.rows.length === 0) {
        throw new Error(`File ${fileId} not found or already deleted`);
      }

      // Check for dependencies (released versions)
      const releasedVersions = await this.versionControlService.getReleasedVersions(fileId);
      if (releasedVersions.length > 0) {
        throw new Error(`Cannot delete file ${fileId} - it has released versions`);
      }

      // Soft delete the file (mark as deleted instead of removing)
      await client.query(
        'UPDATE files SET is_deleted = true WHERE id = $1',
        [fileId]
      );

      await client.query('COMMIT');

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * List files in a project with optional filters
   */
  async listFiles(projectId: string, filters?: FileFilters): Promise<FileRecord[]> {
    if (!projectId || projectId.trim().length === 0) {
      throw new Error('Project ID is required');
    }

    // Build query with filters
    let query = `
      SELECT f.*, u.username as uploaded_by_username
      FROM files f
      LEFT JOIN users u ON f.uploaded_by = u.id
      WHERE f.project_id = $1 AND f.is_deleted = false
    `;
    const queryParams: any[] = [projectId];
    let paramIndex = 2;

    if (filters?.fileType) {
      query += ` AND f.file_type = $${paramIndex}`;
      queryParams.push(filters.fileType);
      paramIndex++;
    }

    if (filters?.dateFrom) {
      query += ` AND f.uploaded_at >= $${paramIndex}`;
      queryParams.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters?.dateTo) {
      query += ` AND f.uploaded_at <= $${paramIndex}`;
      queryParams.push(filters.dateTo);
      paramIndex++;
    }

    if (filters?.author) {
      query += ` AND u.username ILIKE $${paramIndex}`;
      queryParams.push(`%${filters.author}%`);
      paramIndex++;
    }

    query += ' ORDER BY f.uploaded_at DESC';

    const result = await this.db.query(query, queryParams);

    // Convert database rows to FileRecord objects
    const fileRecords: FileRecord[] = [];

    for (const row of result.rows) {
      // Get version history for each file
      const versions = await this.versionControlService.getVersionHistory(row.id);

      const fileRecord: FileRecord = {
        id: row.id,
        projectId: row.project_id,
        name: row.name,
        originalName: row.original_name,
        fileType: row.file_type,
        size: parseInt(row.size),
        checksum: row.checksum,
        uploadedBy: row.uploaded_by,
        uploadedAt: row.uploaded_at,
        currentVersion: row.current_version,
        metadata: row.metadata || {},
        versions: versions
      };

      fileRecords.push(fileRecord);
    }

    return fileRecords;
  }

  /**
   * Extract metadata from a file
   */
  async extractMetadata(file: Express.Multer.File): Promise<FileMetadata> {
    if (!file) {
      throw new Error('File is required');
    }

    const fileType = this.fileUploadService.getFileTypeFromExtension(file.originalname);
    if (!fileType) {
      throw new Error('Unable to determine file type from extension');
    }

    const metadata: FileMetadata = {
      title: file.originalname,
      author: 'Unknown', // Would be extracted from file content
      createdDate: new Date(),
    };

    // Enhanced metadata extraction using FileParserService
    try {
      // Create a temporary file for parsing
      const tempFilePath = path.join(os.tmpdir(), `temp_${Date.now()}_${file.originalname}`);
      await writeFile(tempFilePath, file.buffer);

      try {
        switch (fileType) {
          case 'pdf':
            const pdfMetadata = await this.fileParserService.parsePDFMetadata(tempFilePath);
            Object.assign(metadata, pdfMetadata);
            break;

          case 'dxf':
            const dxfMetadata = await this.fileParserService.parseDXFMetadata(tempFilePath);
            Object.assign(metadata, dxfMetadata);
            break;

          case 'e3series':
            // E3 series metadata extraction would be implemented here
            // For now, just set basic metadata
            metadata.layers = [];
            break;
        }
      } finally {
        // Clean up temporary file
        try {
          await unlink(tempFilePath);
        } catch (cleanupError) {
          // Log cleanup error but don't fail the operation
          console.warn('Failed to clean up temporary file:', cleanupError);
        }
      }
    } catch (error) {
      // If metadata extraction fails, log the error but don't fail the upload
      console.warn('Failed to extract metadata:', error);

      // Set basic fallback metadata
      switch (fileType) {
        case 'pdf':
          metadata.pageCount = 1;
          break;
        case 'dxf':
          metadata.layers = [];
          metadata.dimensions = { width: 0, height: 0 };
          break;
        case 'e3series':
          metadata.layers = [];
          break;
      }
    }

    return metadata;
  }

  /**
   * Verify file integrity
   */
  async verifyFileIntegrity(fileId: string, version?: string): Promise<boolean> {
    if (!fileId || fileId.trim().length === 0) {
      throw new Error('File ID is required');
    }

    // Get the specific version or latest version
    let versionRecord;
    if (version) {
      versionRecord = await this.versionControlService.getVersion(fileId, version);
      if (!versionRecord) {
        throw new Error(`Version ${version} of file ${fileId} not found`);
      }
    } else {
      versionRecord = await this.versionControlService.getLatestVersion(fileId);
      if (!versionRecord) {
        throw new Error(`No versions found for file ${fileId}`);
      }
    }

    // Verify file exists and calculate checksum
    try {
      const calculatedChecksum = await this.fileStorageService.calculateChecksumFromFile(versionRecord.filePath);

      // Get stored checksum from database
      const versionResult = await this.db.query(
        'SELECT checksum FROM versions WHERE id = $1',
        [versionRecord.id]
      );

      if (versionResult.rows.length === 0) {
        return false;
      }

      const storedChecksum = versionResult.rows[0].checksum;
      return calculatedChecksum === storedChecksum;

    } catch (error) {
      // File doesn't exist or can't be read
      return false;
    }
  }

  /**
   * Get file statistics
   */
  async getFileStats(fileId: string): Promise<{
    size: number;
    createdAt: Date;
    modifiedAt: Date;
    versions: number;
  }> {
    if (!fileId || fileId.trim().length === 0) {
      throw new Error('File ID is required');
    }

    // Get file record
    const fileResult = await this.db.query(
      'SELECT size, uploaded_at FROM files WHERE id = $1 AND is_deleted = false',
      [fileId]
    );

    if (fileResult.rows.length === 0) {
      throw new Error(`File ${fileId} not found`);
    }

    const fileRecord = fileResult.rows[0];

    // Get version statistics
    const versionStats = await this.versionControlService.getVersionStatistics(fileId);

    // Get latest version date as modified date
    const latestVersion = await this.versionControlService.getLatestVersion(fileId);

    return {
      size: parseInt(fileRecord.size),
      createdAt: fileRecord.uploaded_at,
      modifiedAt: latestVersion?.createdAt || fileRecord.uploaded_at,
      versions: versionStats.totalVersions
    };
  }

  /**
   * Check if file exists
   */
  async fileExists(fileId: string, version?: string): Promise<boolean> {
    if (!fileId || fileId.trim().length === 0) {
      throw new Error('File ID is required');
    }

    try {
      // Check if file record exists
      const fileResult = await this.db.query(
        'SELECT id FROM files WHERE id = $1 AND is_deleted = false',
        [fileId]
      );

      if (fileResult.rows.length === 0) {
        return false;
      }

      // If version is specified, check if that version exists
      if (version) {
        const versionRecord = await this.versionControlService.getVersion(fileId, version);
        return versionRecord !== null;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): ('e3series' | 'pdf' | 'dxf')[] {
    return ['e3series', 'pdf', 'dxf'];
  }

  /**
   * Get maximum file size allowed
   */
  getMaxFileSize(): number {
    return 1024 * 1024 * 1024; // 1GB
  }

  /**
   * Validate file before processing
   */
  async validateFile(file: Express.Multer.File): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    if (!file) {
      return {
        isValid: false,
        errors: ['No file provided'],
        warnings: []
      };
    }

    // Use upload service validation
    const uploadValidation = await this.fileUploadService.validateFileBeforeUpload(file);
    
    // Additional business logic validation can be added here
    const warnings: string[] = [];
    
    // Check for common issues
    if (file.originalname.includes(' ')) {
      warnings.push('File name contains spaces, which may cause issues in some systems');
    }

    if (file.originalname.length > 100) {
      warnings.push('File name is quite long, consider using a shorter name');
    }

    return {
      isValid: uploadValidation.isValid,
      errors: uploadValidation.errors,
      warnings
    };
  }

  /**
   * Create a new version of an existing file
   */
  async createNewVersion(
    fileId: string,
    file: Express.Multer.File,
    changes: string,
    modifiedBy: string
  ): Promise<FileRecord> {
    const client = await this.db.connect();

    try {
      await client.query('BEGIN');

      // Verify file exists
      const fileResult = await this.db.query(
        'SELECT * FROM files WHERE id = $1 AND is_deleted = false',
        [fileId]
      );

      if (fileResult.rows.length === 0) {
        throw new Error(`File ${fileId} not found`);
      }

      const existingFile = fileResult.rows[0];

      // Validate file type matches
      const fileType = this.fileUploadService.getFileTypeFromExtension(file.originalname);
      if (fileType !== existingFile.file_type) {
        throw new Error(`File type mismatch. Expected ${existingFile.file_type}, got ${fileType}`);
      }

      // Extract metadata from new file
      const metadata = await this.extractMetadata(file);

      // Create new version first to get version number
      const versionChanges: VersionChanges = {
        description: changes,
        modifiedBy: modifiedBy
      };

      const version = await this.versionControlService.createVersion(fileId, versionChanges);

      // Store the new file version
      const checksum = this.fileStorageService.calculateChecksum(file.buffer);
      const fileData = {
        buffer: file.buffer,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size
      };
      const filePath = await this.fileStorageService.storeFile(existingFile.project_id, fileData, fileId, version.version);

      // Update version with file information
      await client.query(
        'UPDATE versions SET file_size = $1, checksum = $2, file_path = $3 WHERE id = $4',
        [file.size, checksum, filePath, version.id]
      );

      // Update file metadata
      await client.query(
        'UPDATE files SET metadata = $1, current_version = $2 WHERE id = $3',
        [JSON.stringify(metadata), version.version, fileId]
      );

      // Get updated file record
      const updatedFileResult = await client.query(
        'SELECT * FROM files WHERE id = $1',
        [fileId]
      );

      const updatedFile = updatedFileResult.rows[0];
      const versions = await this.versionControlService.getVersionHistory(fileId);

      const fileRecord: FileRecord = {
        id: updatedFile.id,
        projectId: updatedFile.project_id,
        name: updatedFile.name,
        originalName: updatedFile.original_name,
        fileType: updatedFile.file_type,
        size: parseInt(updatedFile.size),
        checksum: updatedFile.checksum,
        uploadedBy: updatedFile.uploaded_by,
        uploadedAt: updatedFile.uploaded_at,
        currentVersion: updatedFile.current_version,
        metadata: updatedFile.metadata || {},
        versions: versions
      };

      await client.query('COMMIT');
      return fileRecord;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get file by ID
   */
  async getFile(fileId: string): Promise<FileRecord | null> {
    if (!fileId || fileId.trim().length === 0) {
      throw new Error('File ID is required');
    }

    const fileResult = await this.db.query(
      'SELECT * FROM files WHERE id = $1 AND is_deleted = false',
      [fileId]
    );

    if (fileResult.rows.length === 0) {
      return null;
    }

    const row = fileResult.rows[0];
    const versions = await this.versionControlService.getVersionHistory(fileId);

    return {
      id: row.id,
      projectId: row.project_id,
      name: row.name,
      originalName: row.original_name,
      fileType: row.file_type,
      size: parseInt(row.size),
      checksum: row.checksum,
      uploadedBy: row.uploaded_by,
      uploadedAt: row.uploaded_at,
      currentVersion: row.current_version,
      metadata: row.metadata || {},
      versions: versions
    };
  }

  /**
   * Search files across projects
   */
  async searchFiles(searchTerm: string, filters?: FileFilters): Promise<FileRecord[]> {
    if (!searchTerm || searchTerm.trim().length === 0) {
      throw new Error('Search term is required');
    }

    let query = `
      SELECT f.*, u.username as uploaded_by_username
      FROM files f
      LEFT JOIN users u ON f.uploaded_by = u.id
      WHERE f.is_deleted = false
      AND (f.name ILIKE $1 OR f.original_name ILIKE $1 OR f.metadata::text ILIKE $1)
    `;
    const queryParams: any[] = [`%${searchTerm}%`];
    let paramIndex = 2;

    if (filters?.fileType) {
      query += ` AND f.file_type = $${paramIndex}`;
      queryParams.push(filters.fileType);
      paramIndex++;
    }

    if (filters?.dateFrom) {
      query += ` AND f.uploaded_at >= $${paramIndex}`;
      queryParams.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters?.dateTo) {
      query += ` AND f.uploaded_at <= $${paramIndex}`;
      queryParams.push(filters.dateTo);
      paramIndex++;
    }

    query += ' ORDER BY f.uploaded_at DESC LIMIT 100';

    const result = await this.db.query(query, queryParams);

    const fileRecords: FileRecord[] = [];

    for (const row of result.rows) {
      const versions = await this.versionControlService.getVersionHistory(row.id);

      const fileRecord: FileRecord = {
        id: row.id,
        projectId: row.project_id,
        name: row.name,
        originalName: row.original_name,
        fileType: row.file_type,
        size: parseInt(row.size),
        checksum: row.checksum,
        uploadedBy: row.uploaded_by,
        uploadedAt: row.uploaded_at,
        currentVersion: row.current_version,
        metadata: row.metadata || {},
        versions: versions
      };

      fileRecords.push(fileRecord);
    }

    return fileRecords;
  }
}