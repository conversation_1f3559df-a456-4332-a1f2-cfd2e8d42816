import multer from 'multer';
import { FileStorageService } from './FileStorageService';
import { FileRecord, FileMetadata } from '../types';
export interface UploadConfig {
    maxFileSize: number;
    allowedFileTypes: ('e3series' | 'pdf' | 'dxf')[];
    tempStoragePath?: string;
}
export declare class FileUploadService {
    private fileStorageService;
    private uploadConfig;
    constructor(fileStorageService: FileStorageService, config?: Partial<UploadConfig>);
    createUploadMiddleware(): multer.Multer;
    processUpload(file: Express.Multer.File, projectId: string, uploadedBy: string, fileType: 'e3series' | 'pdf' | 'dxf', metadata?: Partial<FileMetadata>): Promise<FileRecord>;
    private isValidFileType;
    private sanitizeFileName;
    getFileTypeFromExtension(fileName: string): 'e3series' | 'pdf' | 'dxf' | null;
    validateFileBeforeUpload(file: Express.Multer.File): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
}
//# sourceMappingURL=FileUploadService.d.ts.map