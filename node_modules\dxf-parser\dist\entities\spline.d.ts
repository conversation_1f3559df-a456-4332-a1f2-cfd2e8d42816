import DxfArrayScanner, { IGroup } from '../DxfArrayScanner';
import IGeometry, { IEntity, IPoint } from './geomtry';
export interface ISplineEntity extends IEntity {
    controlPoints?: IPoint[];
    fitPoints?: IPoint[];
    startTangent: IPoint;
    endTangent: IPoint;
    knotValues: number[];
    closed: boolean;
    periodic: boolean;
    rational: boolean;
    planar: boolean;
    linear: boolean;
    degreeOfSplineCurve: number;
    numberOfKnots: number;
    numberOfControlPoints: number;
    numberOfFitPoints: number;
    normalVector: IPoint;
}
export default class Spline implements IGeometry {
    ForEntityName: "SPLINE";
    parseEntity(scanner: DxfArrayScanner, curr: IGroup): ISplineEntity;
}
