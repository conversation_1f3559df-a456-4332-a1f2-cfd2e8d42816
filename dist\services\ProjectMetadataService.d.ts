import { Pool } from 'pg';
import { AuthorizationService } from './AuthorizationService';
export interface MetadataChange {
    id: string;
    projectId: string;
    field: string;
    oldValue: any;
    newValue: any;
    changedBy: string;
    changedAt: Date;
    reason?: string;
}
export interface MetadataValidationRule {
    field: string;
    type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    allowedValues?: any[];
    customValidator?: (value: any) => {
        isValid: boolean;
        error?: string;
    };
}
export declare class ProjectMetadataService {
    private db;
    private authService;
    private validationRules;
    constructor(database: Pool, authorizationService: AuthorizationService);
    private initializeDefaultValidationRules;
    addValidationRules(projectType: string, rules: MetadataValidationRule[]): void;
    validateMetadata(metadata: Record<string, any>, projectType?: string): {
        isValid: boolean;
        errors: string[];
    };
    private validateType;
    updateMetadata(projectId: string, metadataUpdates: Record<string, any>, updatedBy: string, reason?: string): Promise<void>;
    getMetadataHistory(projectId: string, userId: string, limit?: number): Promise<MetadataChange[]>;
    revertMetadataField(projectId: string, field: string, changeId: string, revertedBy: string, reason?: string): Promise<void>;
}
//# sourceMappingURL=ProjectMetadataService.d.ts.map