import { E3ProjectData, PDFMetadata, DXFMetadata, FileValidationResult } from '../types';
import { FileParserService as IFileParserService } from '../types/services';
export declare class FileParsingError extends Error {
    readonly fileType: string;
    readonly filePath?: string;
    readonly suggestions: string[];
    constructor(message: string, fileType: string, filePath?: string, suggestions?: string[]);
}
export declare class FileParserService implements IFileParserService {
    parseE3File(filePath: string): Promise<E3ProjectData>;
    parsePDFMetadata(filePath: string): Promise<PDFMetadata>;
    parseDXFMetadata(filePath: string): Promise<DXFMetadata>;
    validateFileFormat(file: File): Promise<FileValidationResult>;
    private parseE3XmlContent;
    private extractDxfMetadata;
    private extractDxfLayers;
    private extractDxfBlocks;
    private extractDxfUnits;
    private calculateDxfDimensions;
    private mapDxfUnits;
    private validateE3Content;
    private validatePdfContent;
    private validateDxfContent;
    private normalizeMetadataString;
    private parseMetadataDate;
    private validateAndNormalizeMetadata;
    private readFileContent;
}
//# sourceMappingURL=FileParserService.d.ts.map