"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Version = void 0;
class Version {
    constructor(data) {
        this.id = data.id;
        this.fileId = data.fileId;
        this.version = data.version;
        this.createdBy = data.createdBy;
        this.createdAt = data.createdAt;
        this.changes = data.changes;
        this.isLocked = data.isLocked;
        this.isReleased = data.isReleased;
        this.filePath = data.filePath;
    }
    static validate(data) {
        const errors = [];
        if (!data.fileId || typeof data.fileId !== 'string' || data.fileId.trim().length === 0) {
            errors.push('FileId is required and must be a non-empty string');
        }
        if (!data.version || typeof data.version !== 'string' || data.version.trim().length === 0) {
            errors.push('Version is required and must be a non-empty string');
        }
        if (data.version && !/^\d+\.\d+\.\d+$/.test(data.version)) {
            errors.push('Version must follow semantic versioning format (e.g., "1.0.0")');
        }
        if (!data.createdBy || typeof data.createdBy !== 'string' || data.createdBy.trim().length === 0) {
            errors.push('CreatedBy is required and must be a non-empty string');
        }
        if (!data.createdAt || !(data.createdAt instanceof Date)) {
            errors.push('CreatedAt is required and must be a valid Date object');
        }
        if (!data.changes || typeof data.changes !== 'string') {
            errors.push('Changes description is required and must be a string');
        }
        if (data.changes && data.changes.length > 1000) {
            errors.push('Changes description must not exceed 1000 characters');
        }
        if (data.isLocked !== undefined && typeof data.isLocked !== 'boolean') {
            errors.push('IsLocked must be a boolean value');
        }
        if (data.isReleased !== undefined && typeof data.isReleased !== 'boolean') {
            errors.push('IsReleased must be a boolean value');
        }
        if (!data.filePath || typeof data.filePath !== 'string' || data.filePath.trim().length === 0) {
            errors.push('FilePath is required and must be a non-empty string');
        }
        if (data.filePath && data.filePath.length > 500) {
            errors.push('FilePath must not exceed 500 characters');
        }
        if (data.isReleased && !data.isLocked) {
            errors.push('Released versions must be locked');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    static create(data) {
        const versionData = {
            id: data.id || crypto.randomUUID(),
            fileId: data.fileId,
            version: data.version,
            createdBy: data.createdBy,
            createdAt: new Date(),
            changes: data.changes,
            isLocked: data.isLocked || false,
            isReleased: data.isReleased || false,
            filePath: data.filePath
        };
        const validation = Version.validate(versionData);
        if (!validation.isValid) {
            throw new Error(`Invalid version data: ${validation.errors.join(', ')}`);
        }
        return new Version(versionData);
    }
    lock() {
        if (this.isLocked) {
            throw new Error('Version is already locked');
        }
        this.isLocked = true;
    }
    unlock() {
        if (this.isReleased) {
            throw new Error('Cannot unlock a released version');
        }
        this.isLocked = false;
    }
    release() {
        if (this.isReleased) {
            throw new Error('Version is already released');
        }
        this.isReleased = true;
        this.isLocked = true;
    }
    updateChanges(changes) {
        if (this.isLocked) {
            throw new Error('Cannot update changes for a locked version');
        }
        if (!changes || typeof changes !== 'string') {
            throw new Error('Changes description is required and must be a string');
        }
        if (changes.length > 1000) {
            throw new Error('Changes description must not exceed 1000 characters');
        }
        this.changes = changes;
    }
    static compareVersions(version1, version2) {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);
        for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
            const v1Part = v1Parts[i] || 0;
            const v2Part = v2Parts[i] || 0;
            if (v1Part > v2Part)
                return 1;
            if (v1Part < v2Part)
                return -1;
        }
        return 0;
    }
    static getNextVersion(currentVersion, type = 'patch') {
        const parts = currentVersion.split('.').map(Number);
        switch (type) {
            case 'major':
                return `${(parts[0] || 0) + 1}.0.0`;
            case 'minor':
                return `${parts[0] || 0}.${(parts[1] || 0) + 1}.0`;
            case 'patch':
            default:
                return `${parts[0] || 0}.${parts[1] || 0}.${(parts[2] || 0) + 1}`;
        }
    }
    static isValidVersionFormat(version) {
        return /^\d+\.\d+\.\d+$/.test(version);
    }
}
exports.Version = Version;
//# sourceMappingURL=Version.js.map