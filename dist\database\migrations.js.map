{"version": 3, "file": "migrations.js", "sourceRoot": "", "sources": ["../../src/database/migrations.ts"], "names": [], "mappings": ";;;AACA,2BAA+C;AAC/C,+BAA4B;AAW5B,MAAa,gBAAgB;IAI3B,YAAY,EAAsB,EAAE,cAAuB;QACzD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,IAAA,WAAI,EAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACxE,CAAC;IAKO,KAAK,CAAC,yBAAyB;QACrC,MAAM,gBAAgB,GAAG;;;;;;;;;;;KAWxB,CAAC;QAEF,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACxC,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAAY;;;;KAI7C,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAKO,kBAAkB;QACxB,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAA,gBAAW,EAAC,IAAI,CAAC,cAAc,CAAC;iBAC3C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACrC,IAAI,EAAE,CAAC;YAEV,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBACjD,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAG/C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBACtD,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,CAAC,IAAI,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;oBACzD,SAAS;gBACX,CAAC;gBAED,MAAM,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;gBAChC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;oBACtB,OAAO,CAAC,IAAI,CAAC,kCAAkC,IAAI,EAAE,CAAC,CAAC;oBACvD,SAAS;gBACX,CAAC;gBAGD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACjD,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBACjE,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBAE9B,UAAU,CAAC,IAAI,CAAC;oBACd,EAAE,EAAE,GAAG,OAAO,IAAI,IAAI,EAAE;oBACxB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;oBAC7B,OAAO;oBACP,EAAE;oBACF,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,iEAAiE,EAAE,KAAK,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAKO,iBAAiB,CAAC,OAAe;QACvC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,SAAoB;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAEtD,MAAM,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAEzC,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAGjC,MAAM,MAAM,CAAC,KAAK,CAChB;iCACyB,EACzB,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAC5D,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,EAAE,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IACxE,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,SAAoB;QAClD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,+CAA+C,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,MAAM,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAEzC,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAK,CAAC,CAAC;YAGpC,MAAM,MAAM,CAAC,KAAK,CAChB,6CAA6C,EAC7C,CAAC,SAAS,CAAC,EAAE,CAAC,CACf,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,SAAS,CAAC,EAAE,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5E,CAAC;IAKM,KAAK,CAAC,OAAO;QAClB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAEtD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,YAAY,iBAAiB,CAAC,MAAM,wBAAwB,CAAC,CAAC;QAE1E,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAKM,KAAK,CAAC,QAAQ,CAAC,aAAsB;QAC1C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAGtD,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAGtE,IAAI,oBAAiC,CAAC;QAEtC,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,aAAa,CAAC,CAAC;YAClF,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,kBAAkB,aAAa,kCAAkC,CAAC,CAAC;YACrF,CAAC;YACD,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC5E,CAAC;aAAM,CAAC;YAEN,oBAAoB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,oBAAoB,CAAC,MAAM,gBAAgB,CAAC,CAAC;QAEzE,KAAK,MAAM,gBAAgB,IAAI,oBAAoB,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;IAKM,KAAK,CAAC,SAAS;QAIpB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAEtD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjF,OAAO;YACL,OAAO,EAAE,iBAAiB;YAC1B,OAAO,EAAE,iBAAiB;SAC3B,CAAC;IACJ,CAAC;IAKM,KAAK,CAAC,sBAAsB;QACjC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9E,MAAM,WAAW,GAAG,GAAG,SAAS,iBAAiB,CAAC;QAElD,MAAM,gBAAgB,GAAG;;;EAG3B,IAAA,iBAAY,EAAC,IAAA,WAAI,EAAC,SAAS,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;CAgBpD,CAAC;QAGE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACxC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,aAAa,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,cAAc,EAAE,GAAG,WAAW,MAAM,CAAC,CAAC;QACtE,OAAO,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;IAC7D,CAAC;CACF;AA3QD,4CA2QC;AAGM,MAAM,sBAAsB,GAAG,CAAC,EAAsB,EAAE,EAAE;IAC/D,OAAO,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAClC,CAAC,CAAC;AAFW,QAAA,sBAAsB,0BAEjC"}