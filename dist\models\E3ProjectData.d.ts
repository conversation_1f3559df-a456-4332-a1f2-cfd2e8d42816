import { E3ProjectData as IE3ProjectData, Component, Connection, Layer, Sheet } from '../types';
export declare class E3ProjectData implements IE3ProjectData {
    components: Component[];
    connections: Connection[];
    properties: Record<string, any>;
    layers: Layer[];
    sheets: Sheet[];
    constructor(data: IE3ProjectData);
    static validate(data: Partial<IE3ProjectData>): {
        isValid: boolean;
        errors: string[];
    };
    static validateComponent(component: Partial<Component>): string[];
    static validateConnection(connection: Partial<Connection>): string[];
    static validateLayer(layer: Partial<Layer>): string[];
    static validateSheet(sheet: Partial<Sheet>): string[];
    static create(data?: Partial<IE3ProjectData>): E3ProjectData;
    addComponent(component: Component): void;
    removeComponent(componentId: string): void;
    addConnection(connection: Connection): void;
    removeConnection(connectionId: string): void;
    getComponentsOnSheet(sheetId: string): Component[];
    getConnectionsOnSheet(sheetId: string): Connection[];
}
//# sourceMappingURL=E3ProjectData.d.ts.map