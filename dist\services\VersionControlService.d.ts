import { Pool } from 'pg';
import { Version } from '../models/Version';
import { VersionChanges, VersionDiff } from '../types';
export declare class VersionControlService {
    private db;
    constructor(db: Pool);
    createVersion(fileId: string, changes: VersionChanges): Promise<Version>;
    getVersionHistory(fileId: string): Promise<Version[]>;
    getVersion(fileId: string, version: string): Promise<Version | null>;
    lockVersion(fileId: string, version: string): Promise<void>;
    unlockVersion(fileId: string, version: string): Promise<void>;
    releaseVersion(fileId: string, version: string): Promise<void>;
    compareVersions(fileId: string, version1: string, version2: string): Promise<VersionDiff>;
    getLatestVersion(fileId: string): Promise<Version | null>;
    getReleasedVersions(fileId: string): Promise<Version[]>;
    canDeleteVersion(fileId: string, version: string): Promise<{
        canDelete: boolean;
        reason?: string;
    }>;
    updateVersionChanges(fileId: string, version: string, changes: string): Promise<void>;
    getVersionStatistics(fileId: string): Promise<{
        totalVersions: number;
        releasedVersions: number;
        lockedVersions: number;
        latestVersion: string;
        firstVersion: string;
    }>;
}
//# sourceMappingURL=VersionControlService.d.ts.map