import { DatabaseConnection } from './connection';
export interface Migration {
    id: string;
    name: string;
    version: string;
    up: string;
    down?: string;
    appliedAt?: Date;
}
export declare class MigrationManager {
    private db;
    private migrationsPath;
    constructor(db: DatabaseConnection, migrationsPath?: string);
    private initializeMigrationsTable;
    private getAppliedMigrations;
    private loadMigrationFiles;
    private calculateChecksum;
    private applyMigration;
    private rollbackMigration;
    migrate(): Promise<void>;
    rollback(targetVersion?: string): Promise<void>;
    getStatus(): Promise<{
        applied: Migration[];
        pending: Migration[];
    }>;
    createInitialMigration(): Promise<void>;
}
export declare const createMigrationManager: (db: DatabaseConnection) => MigrationManager;
//# sourceMappingURL=migrations.d.ts.map