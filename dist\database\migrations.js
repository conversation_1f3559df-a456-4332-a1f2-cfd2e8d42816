"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMigrationManager = exports.MigrationManager = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
class MigrationManager {
    constructor(db, migrationsPath) {
        this.db = db;
        this.migrationsPath = migrationsPath || (0, path_1.join)(__dirname, 'migrations');
    }
    async initializeMigrationsTable() {
        const createTableQuery = `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(64) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_schema_migrations_version ON schema_migrations(version);
      CREATE INDEX IF NOT EXISTS idx_schema_migrations_applied_at ON schema_migrations(applied_at);
    `;
        await this.db.query(createTableQuery);
    }
    async getAppliedMigrations() {
        await this.initializeMigrationsTable();
        const result = await this.db.query(`
      SELECT id, name, version, applied_at as "appliedAt"
      FROM schema_migrations
      ORDER BY applied_at ASC
    `);
        return result.rows;
    }
    loadMigrationFiles() {
        const migrations = [];
        try {
            const files = (0, fs_1.readdirSync)(this.migrationsPath)
                .filter(file => file.endsWith('.sql'))
                .sort();
            for (const file of files) {
                const filePath = (0, path_1.join)(this.migrationsPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf8');
                const match = file.match(/^(\d{8}_\d{6})_(.+)\.sql$/);
                if (!match) {
                    console.warn(`Skipping invalid migration file: ${file}`);
                    continue;
                }
                const [, version, name] = match;
                if (!version || !name) {
                    console.warn(`Invalid migration file format: ${file}`);
                    continue;
                }
                const parts = content.split('-- DOWN MIGRATION');
                const up = parts[0]?.replace('-- UP MIGRATION', '').trim() || '';
                const down = parts[1]?.trim();
                migrations.push({
                    id: `${version}_${name}`,
                    name: name.replace(/_/g, ' '),
                    version,
                    up,
                    ...(down && { down }),
                });
            }
        }
        catch (error) {
            console.warn('No migration files found or error reading migrations directory:', error);
        }
        return migrations;
    }
    calculateChecksum(content) {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(content).digest('hex');
    }
    async applyMigration(migration) {
        const checksum = this.calculateChecksum(migration.up);
        await this.db.transaction(async (client) => {
            await client.query(migration.up);
            await client.query(`INSERT INTO schema_migrations (id, name, version, checksum) 
         VALUES ($1, $2, $3, $4)`, [migration.id, migration.name, migration.version, checksum]);
        });
        console.log(`Applied migration: ${migration.id} - ${migration.name}`);
    }
    async rollbackMigration(migration) {
        if (!migration.down) {
            throw new Error(`No rollback script available for migration: ${migration.id}`);
        }
        await this.db.transaction(async (client) => {
            await client.query(migration.down);
            await client.query('DELETE FROM schema_migrations WHERE id = $1', [migration.id]);
        });
        console.log(`Rolled back migration: ${migration.id} - ${migration.name}`);
    }
    async migrate() {
        const appliedMigrations = await this.getAppliedMigrations();
        const availableMigrations = this.loadMigrationFiles();
        const appliedIds = new Set(appliedMigrations.map(m => m.id));
        const pendingMigrations = availableMigrations.filter(m => !appliedIds.has(m.id));
        if (pendingMigrations.length === 0) {
            console.log('No pending migrations to apply');
            return;
        }
        console.log(`Applying ${pendingMigrations.length} pending migrations...`);
        for (const migration of pendingMigrations) {
            await this.applyMigration(migration);
        }
        console.log('All migrations applied successfully');
    }
    async rollback(targetVersion) {
        const appliedMigrations = await this.getAppliedMigrations();
        const availableMigrations = this.loadMigrationFiles();
        const migrationMap = new Map(availableMigrations.map(m => [m.id, m]));
        let migrationsToRollback;
        if (targetVersion) {
            const targetIndex = appliedMigrations.findIndex(m => m.version === targetVersion);
            if (targetIndex === -1) {
                throw new Error(`Target version ${targetVersion} not found in applied migrations`);
            }
            migrationsToRollback = appliedMigrations.slice(targetIndex + 1).reverse();
        }
        else {
            migrationsToRollback = appliedMigrations.slice(-1);
        }
        if (migrationsToRollback.length === 0) {
            console.log('No migrations to rollback');
            return;
        }
        console.log(`Rolling back ${migrationsToRollback.length} migrations...`);
        for (const appliedMigration of migrationsToRollback) {
            const migration = migrationMap.get(appliedMigration.id);
            if (!migration) {
                throw new Error(`Migration file not found for: ${appliedMigration.id}`);
            }
            await this.rollbackMigration(migration);
        }
        console.log('Rollback completed successfully');
    }
    async getStatus() {
        const appliedMigrations = await this.getAppliedMigrations();
        const availableMigrations = this.loadMigrationFiles();
        const appliedIds = new Set(appliedMigrations.map(m => m.id));
        const pendingMigrations = availableMigrations.filter(m => !appliedIds.has(m.id));
        return {
            applied: appliedMigrations,
            pending: pendingMigrations,
        };
    }
    async createInitialMigration() {
        const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 15);
        const migrationId = `${timestamp}_initial_schema`;
        const migrationContent = `-- UP MIGRATION
-- Initial schema creation for E3 PDM System

${(0, fs_1.readFileSync)((0, path_1.join)(__dirname, 'schema.sql'), 'utf8')}

-- DOWN MIGRATION
-- Drop all tables and extensions
DROP TABLE IF EXISTS audit_trail CASCADE;
DROP TABLE IF EXISTS versions CASCADE;
DROP TABLE IF EXISTS files CASCADE;
DROP TABLE IF EXISTS project_permissions CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS schema_migrations CASCADE;

DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS create_audit_trail() CASCADE;

DROP EXTENSION IF EXISTS "uuid-ossp";
`;
        const fs = require('fs');
        if (!fs.existsSync(this.migrationsPath)) {
            fs.mkdirSync(this.migrationsPath, { recursive: true });
        }
        const migrationFile = (0, path_1.join)(this.migrationsPath, `${migrationId}.sql`);
        require('fs').writeFileSync(migrationFile, migrationContent);
        console.log(`Created initial migration: ${migrationFile}`);
    }
}
exports.MigrationManager = MigrationManager;
const createMigrationManager = (db) => {
    return new MigrationManager(db);
};
exports.createMigrationManager = createMigrationManager;
//# sourceMappingURL=migrations.js.map