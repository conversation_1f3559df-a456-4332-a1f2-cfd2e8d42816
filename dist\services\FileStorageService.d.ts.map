{"version": 3, "file": "FileStorageService.d.ts", "sourceRoot": "", "sources": ["../../src/services/FileStorageService.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAIzB,OAAO,EAAE,oBAAoB,EAAE,MAAM,UAAU,CAAC;AAShD,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,aAAa;IAC5B,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,gBAAgB,EAAE;QAChB,UAAU,EAAE,MAAM,EAAE,CAAC;QACrB,KAAK,EAAE,MAAM,EAAE,CAAC;QAChB,KAAK,EAAE,MAAM,EAAE,CAAC;KACjB,CAAC;CACH;AAED,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,MAAM,CAAgB;gBAElB,MAAM,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;IAerC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,SAAS,CACb,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,cAAc,EACxB,MAAM,EAAE,MAAM,EACd,OAAO,GAAE,MAAgB,GACxB,OAAO,CAAC,MAAM,CAAC;IA+BlB,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC,cAAc;IAOhD,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAY/C,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAY3C,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAY9C,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;IAWvD,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAOnC,yBAAyB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAQlE,kBAAkB,CAAC,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,GAAG,KAAK,GAAG,KAAK,GAAG,oBAAoB;IA0C5G,OAAO,CAAC,qBAAqB;IAY7B,OAAO,CAAC,mBAAmB;IAsCrB,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAavF,qBAAqB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAOhD,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;IAOvD,uBAAuB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAgB9D"}