"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectManagementService = exports.ProjectManagementError = void 0;
const Project_1 = require("../models/Project");
class ProjectManagementError extends Error {
    constructor(message, code, projectId, userId) {
        super(message);
        this.name = 'ProjectManagementError';
        this.code = code;
        if (projectId !== undefined) {
            this.projectId = projectId;
        }
        if (userId !== undefined) {
            this.userId = userId;
        }
    }
}
exports.ProjectManagementError = ProjectManagementError;
class ProjectManagementService {
    constructor(database, authorizationService) {
        this.db = database;
        this.authService = authorizationService;
    }
    async createProject(projectData, createdBy) {
        try {
            const validation = Project_1.Project.validate({
                ...projectData,
                id: '',
                createdBy,
                createdAt: new Date(),
                updatedAt: new Date(),
                status: 'active',
                permissions: []
            });
            if (!validation.isValid) {
                throw new ProjectManagementError(`Invalid project data: ${validation.errors.join(', ')}`, 'VALIDATION_ERROR');
            }
            const project = Project_1.Project.create({ ...projectData, createdBy });
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                await client.query(`INSERT INTO projects (id, name, description, created_by, created_at, updated_at, status, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
           RETURNING *`, [
                    project.id,
                    project.name,
                    project.description,
                    project.createdBy,
                    project.createdAt,
                    project.updatedAt,
                    project.status,
                    JSON.stringify(project.metadata)
                ]);
                await client.query(`INSERT INTO project_permissions (project_id, user_id, role, granted_by, granted_at)
           VALUES ($1, $2, $3, $4, $5)`, [project.id, createdBy, 'admin', createdBy, new Date()]);
                await client.query('COMMIT');
                project.addPermission({
                    userId: createdBy,
                    role: 'admin',
                    grantedBy: createdBy
                });
                return project;
            }
            catch (error) {
                await client.query('ROLLBACK');
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'CREATE_ERROR', undefined, createdBy);
        }
    }
    async updateProject(projectId, updates, updatedBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: updatedBy }, 'project', 'write', projectId);
            if (!hasPermission) {
                throw new ProjectManagementError('Insufficient permissions to update project', 'PERMISSION_DENIED', projectId, updatedBy);
            }
            const existingProject = await this.getProject(projectId);
            const updatedData = {
                ...existingProject,
                ...updates,
                updatedAt: new Date()
            };
            const validation = Project_1.Project.validate(updatedData);
            if (!validation.isValid) {
                throw new ProjectManagementError(`Invalid project update: ${validation.errors.join(', ')}`, 'VALIDATION_ERROR', projectId, updatedBy);
            }
            const result = await this.db.query(`UPDATE projects 
         SET name = $1, description = $2, metadata = $3, updated_at = $4
         WHERE id = $5 AND status != 'archived'
         RETURNING *`, [
                updatedData.name,
                updatedData.description,
                JSON.stringify(updatedData.metadata),
                updatedData.updatedAt,
                projectId
            ]);
            if (result.rows.length === 0) {
                throw new ProjectManagementError('Project not found or is archived', 'NOT_FOUND', projectId, updatedBy);
            }
            return await this.getProject(projectId);
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to update project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'UPDATE_ERROR', projectId, updatedBy);
        }
    }
    async getProject(projectId, userId) {
        try {
            const projectResult = await this.db.query('SELECT * FROM projects WHERE id = $1', [projectId]);
            if (projectResult.rows.length === 0) {
                throw new ProjectManagementError('Project not found', 'NOT_FOUND', projectId, userId);
            }
            const projectRow = projectResult.rows[0];
            const permissionsResult = await this.db.query(`SELECT user_id, role, granted_by, granted_at 
         FROM project_permissions 
         WHERE project_id = $1`, [projectId]);
            const permissions = permissionsResult.rows.map(row => ({
                userId: row.user_id,
                role: row.role,
                grantedBy: row.granted_by,
                grantedAt: row.granted_at
            }));
            const projectData = {
                id: projectRow.id,
                name: projectRow.name,
                description: projectRow.description,
                createdBy: projectRow.created_by,
                createdAt: projectRow.created_at,
                updatedAt: projectRow.updated_at,
                status: projectRow.status,
                metadata: projectRow.metadata || {},
                permissions
            };
            return new Project_1.Project(projectData);
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to get project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'GET_ERROR', projectId, userId);
        }
    }
    async listProjects(userId) {
        try {
            const result = await this.db.query(`SELECT DISTINCT p.* 
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE p.created_by = $1 OR pp.user_id = $1
         ORDER BY p.updated_at DESC`, [userId]);
            const projects = [];
            for (const row of result.rows) {
                const permissionsResult = await this.db.query(`SELECT user_id, role, granted_by, granted_at 
           FROM project_permissions 
           WHERE project_id = $1`, [row.id]);
                const permissions = permissionsResult.rows.map(permRow => ({
                    userId: permRow.user_id,
                    role: permRow.role,
                    grantedBy: permRow.granted_by,
                    grantedAt: permRow.granted_at
                }));
                const projectData = {
                    id: row.id,
                    name: row.name,
                    description: row.description,
                    createdBy: row.created_by,
                    createdAt: row.created_at,
                    updatedAt: row.updated_at,
                    status: row.status,
                    metadata: row.metadata || {},
                    permissions
                };
                projects.push(new Project_1.Project(projectData));
            }
            return projects;
        }
        catch (error) {
            throw new ProjectManagementError(`Failed to list projects: ${error instanceof Error ? error.message : 'Unknown error'}`, 'LIST_ERROR', undefined, userId);
        }
    }
    async archiveProject(projectId, archivedBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: archivedBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementError('Insufficient permissions to archive project', 'PERMISSION_DENIED', projectId, archivedBy);
            }
            const result = await this.db.query(`UPDATE projects 
         SET status = 'archived', updated_at = $1
         WHERE id = $2 AND status = 'active'
         RETURNING id`, [new Date(), projectId]);
            if (result.rows.length === 0) {
                throw new ProjectManagementError('Project not found or already archived', 'NOT_FOUND', projectId, archivedBy);
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to archive project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ARCHIVE_ERROR', projectId, archivedBy);
        }
    }
    async restoreProject(projectId, restoredBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: restoredBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementError('Insufficient permissions to restore project', 'PERMISSION_DENIED', projectId, restoredBy);
            }
            const result = await this.db.query(`UPDATE projects
         SET status = 'active', updated_at = $1
         WHERE id = $2 AND status = 'archived'
         RETURNING id`, [new Date(), projectId]);
            if (result.rows.length === 0) {
                throw new ProjectManagementError('Project not found or not archived', 'NOT_FOUND', projectId, restoredBy);
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to restore project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'RESTORE_ERROR', projectId, restoredBy);
        }
    }
    async addProjectPermission(projectId, userId, role, grantedBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: grantedBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementError('Insufficient permissions to grant project access', 'PERMISSION_DENIED', projectId, grantedBy);
            }
            const project = await this.getProject(projectId);
            if (project.status === 'archived') {
                throw new ProjectManagementError('Cannot grant permissions to archived project', 'PROJECT_ARCHIVED', projectId, grantedBy);
            }
            await this.db.query(`INSERT INTO project_permissions (project_id, user_id, role, granted_by, granted_at)
         VALUES ($1, $2, $3, $4, $5)
         ON CONFLICT (project_id, user_id)
         DO UPDATE SET role = $3, granted_by = $4, granted_at = $5`, [projectId, userId, role, grantedBy, new Date()]);
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to add project permission: ${error instanceof Error ? error.message : 'Unknown error'}`, 'PERMISSION_ERROR', projectId, grantedBy);
        }
    }
    async removeProjectPermission(projectId, userId, removedBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: removedBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementError('Insufficient permissions to remove project access', 'PERMISSION_DENIED', projectId, removedBy);
            }
            const project = await this.getProject(projectId);
            if (project.createdBy === userId) {
                throw new ProjectManagementError('Cannot remove permissions from project creator', 'INVALID_OPERATION', projectId, removedBy);
            }
            const result = await this.db.query('DELETE FROM project_permissions WHERE project_id = $1 AND user_id = $2', [projectId, userId]);
            if (result.rowCount === 0) {
                throw new ProjectManagementError('User permission not found', 'NOT_FOUND', projectId, removedBy);
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementError(`Failed to remove project permission: ${error instanceof Error ? error.message : 'Unknown error'}`, 'PERMISSION_ERROR', projectId, removedBy);
        }
    }
    async getProjectStats(userId) {
        try {
            const result = await this.db.query(`SELECT
           COUNT(*) as total_projects,
           COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_projects,
           COUNT(CASE WHEN p.status = 'archived' THEN 1 END) as archived_projects,
           COUNT(CASE WHEN p.created_by = $1 THEN 1 END) as owned_projects,
           COUNT(CASE WHEN p.created_by != $1 THEN 1 END) as shared_projects
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE p.created_by = $1 OR pp.user_id = $1`, [userId]);
            const stats = result.rows[0];
            return {
                totalProjects: parseInt(stats.total_projects) || 0,
                activeProjects: parseInt(stats.active_projects) || 0,
                archivedProjects: parseInt(stats.archived_projects) || 0,
                ownedProjects: parseInt(stats.owned_projects) || 0,
                sharedProjects: parseInt(stats.shared_projects) || 0
            };
        }
        catch (error) {
            throw new ProjectManagementError(`Failed to get project statistics: ${error instanceof Error ? error.message : 'Unknown error'}`, 'STATS_ERROR', undefined, userId);
        }
    }
    async searchProjects(userId, query, includeArchived = false) {
        try {
            const statusCondition = includeArchived ? '' : "AND p.status = 'active'";
            const result = await this.db.query(`SELECT DISTINCT p.*
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE (p.created_by = $1 OR pp.user_id = $1)
         AND (p.name ILIKE $2 OR p.description ILIKE $2)
         ${statusCondition}
         ORDER BY p.updated_at DESC`, [userId, `%${query}%`]);
            const projects = [];
            for (const row of result.rows) {
                const permissionsResult = await this.db.query(`SELECT user_id, role, granted_by, granted_at
           FROM project_permissions
           WHERE project_id = $1`, [row.id]);
                const permissions = permissionsResult.rows.map(permRow => ({
                    userId: permRow.user_id,
                    role: permRow.role,
                    grantedBy: permRow.granted_by,
                    grantedAt: permRow.granted_at
                }));
                const projectData = {
                    id: row.id,
                    name: row.name,
                    description: row.description,
                    createdBy: row.created_by,
                    createdAt: row.created_at,
                    updatedAt: row.updated_at,
                    status: row.status,
                    metadata: row.metadata || {},
                    permissions
                };
                projects.push(new Project_1.Project(projectData));
            }
            return projects;
        }
        catch (error) {
            throw new ProjectManagementError(`Failed to search projects: ${error instanceof Error ? error.message : 'Unknown error'}`, 'SEARCH_ERROR', undefined, userId);
        }
    }
}
exports.ProjectManagementService = ProjectManagementService;
//# sourceMappingURL=ProjectManagementService.js.map