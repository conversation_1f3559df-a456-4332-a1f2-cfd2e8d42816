import { Pool } from 'pg';
import { ProjectManagementError } from './ProjectManagementService';
import { AuthorizationService } from './AuthorizationService';
import { User } from '../types';

/**
 * Metadata change record for audit trail
 */
export interface MetadataChange {
  id: string;
  projectId: string;
  field: string;
  oldValue: any;
  newValue: any;
  changedBy: string;
  changedAt: Date;
  reason?: string;
}

/**
 * Metadata validation rule
 */
export interface MetadataValidationRule {
  field: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  allowedValues?: any[];
  customValidator?: (value: any) => { isValid: boolean; error?: string };
}

/**
 * Project metadata management service
 * Handles validation and update functions for project metadata with audit trail
 */
export class ProjectMetadataService {
  private db: Pool;
  private authService: AuthorizationService;
  private validationRules: Map<string, MetadataValidationRule[]>;

  constructor(database: Pool, authorizationService: AuthorizationService) {
    this.db = database;
    this.authService = authorizationService;
    this.validationRules = new Map();
    this.initializeDefaultValidationRules();
  }

  /**
   * Initialize default validation rules for common metadata fields
   */
  private initializeDefaultValidationRules(): void {
    const defaultRules: MetadataValidationRule[] = [
      {
        field: 'projectType',
        type: 'string',
        allowedValues: ['electrical', 'mechanical', 'software', 'mixed']
      },
      {
        field: 'priority',
        type: 'string',
        allowedValues: ['low', 'medium', 'high', 'critical']
      },
      {
        field: 'budget',
        type: 'number',
        min: 0
      },
      {
        field: 'deadline',
        type: 'date'
      },
      {
        field: 'tags',
        type: 'array'
      },
      {
        field: 'customFields',
        type: 'object'
      },
      {
        field: 'version',
        type: 'string',
        pattern: /^\d+\.\d+(\.\d+)?$/
      },
      {
        field: 'department',
        type: 'string',
        minLength: 2,
        maxLength: 50
      },
      {
        field: 'isConfidential',
        type: 'boolean'
      }
    ];

    this.validationRules.set('default', defaultRules);
  }

  /**
   * Adds custom validation rules for a project type
   */
  addValidationRules(projectType: string, rules: MetadataValidationRule[]): void {
    this.validationRules.set(projectType, rules);
  }

  /**
   * Validates metadata against defined rules
   */
  validateMetadata(
    metadata: Record<string, any>,
    projectType: string = 'default'
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const rules = this.validationRules.get(projectType) || this.validationRules.get('default') || [];

    for (const rule of rules) {
      const value = metadata[rule.field];

      // Check required fields
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`Field '${rule.field}' is required`);
        continue;
      }

      // Skip validation if field is not present and not required
      if (value === undefined || value === null) {
        continue;
      }

      // Type validation
      if (!this.validateType(value, rule.type)) {
        errors.push(`Field '${rule.field}' must be of type ${rule.type}`);
        continue;
      }

      // String validations
      if (rule.type === 'string' && typeof value === 'string') {
        if (rule.minLength && value.length < rule.minLength) {
          errors.push(`Field '${rule.field}' must be at least ${rule.minLength} characters long`);
        }
        if (rule.maxLength && value.length > rule.maxLength) {
          errors.push(`Field '${rule.field}' must be no more than ${rule.maxLength} characters long`);
        }
        if (rule.pattern && !rule.pattern.test(value)) {
          errors.push(`Field '${rule.field}' does not match required pattern`);
        }
      }

      // Number validations
      if (rule.type === 'number' && typeof value === 'number') {
        if (rule.min !== undefined && value < rule.min) {
          errors.push(`Field '${rule.field}' must be at least ${rule.min}`);
        }
        if (rule.max !== undefined && value > rule.max) {
          errors.push(`Field '${rule.field}' must be no more than ${rule.max}`);
        }
      }

      // Allowed values validation
      if (rule.allowedValues && !rule.allowedValues.includes(value)) {
        errors.push(`Field '${rule.field}' must be one of: ${rule.allowedValues.join(', ')}`);
      }

      // Custom validation
      if (rule.customValidator) {
        const customResult = rule.customValidator(value);
        if (!customResult.isValid) {
          errors.push(customResult.error || `Field '${rule.field}' failed custom validation`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validates the type of a value
   */
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Updates project metadata with validation and audit trail
   */
  async updateMetadata(
    projectId: string,
    metadataUpdates: Record<string, any>,
    updatedBy: string,
    reason?: string
  ): Promise<void> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: updatedBy } as User,
        'project',
        'write',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to update project metadata',
          'PERMISSION_DENIED',
          projectId,
          updatedBy
        );
      }

      // Get current project
      const projectResult = await this.db.query(
        'SELECT metadata FROM projects WHERE id = $1',
        [projectId]
      );

      if (projectResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found',
          'NOT_FOUND',
          projectId,
          updatedBy
        );
      }

      const currentMetadata = projectResult.rows[0].metadata || {};

      // Merge metadata
      const newMetadata = { ...currentMetadata, ...metadataUpdates };

      // Validate new metadata
      const validation = this.validateMetadata(newMetadata);
      if (!validation.isValid) {
        throw new ProjectManagementError(
          `Metadata validation failed: ${validation.errors.join(', ')}`,
          'VALIDATION_ERROR',
          projectId,
          updatedBy
        );
      }

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Update project metadata
        await client.query(
          'UPDATE projects SET metadata = $1, updated_at = $2 WHERE id = $3',
          [JSON.stringify(newMetadata), new Date(), projectId]
        );

        // Record changes in audit trail
        for (const [field, newValue] of Object.entries(metadataUpdates)) {
          const oldValue = currentMetadata[field];
          
          if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
            await client.query(
              `INSERT INTO project_metadata_changes 
               (id, project_id, field, old_value, new_value, changed_by, changed_at, reason)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
              [
                crypto.randomUUID(),
                projectId,
                field,
                JSON.stringify(oldValue),
                JSON.stringify(newValue),
                updatedBy,
                new Date(),
                reason || null
              ]
            );
          }
        }

        await client.query('COMMIT');

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to update metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UPDATE_ERROR',
        projectId,
        updatedBy
      );
    }
  }

  /**
   * Gets the metadata change history for a project
   */
  async getMetadataHistory(
    projectId: string,
    userId: string,
    limit: number = 50
  ): Promise<MetadataChange[]> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: userId } as User,
        'project',
        'read',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to view metadata history',
          'PERMISSION_DENIED',
          projectId,
          userId
        );
      }

      const result = await this.db.query(
        `SELECT id, project_id, field, old_value, new_value, changed_by, changed_at, reason
         FROM project_metadata_changes 
         WHERE project_id = $1 
         ORDER BY changed_at DESC 
         LIMIT $2`,
        [projectId, limit]
      );

      return result.rows.map(row => ({
        id: row.id,
        projectId: row.project_id,
        field: row.field,
        oldValue: JSON.parse(row.old_value),
        newValue: JSON.parse(row.new_value),
        changedBy: row.changed_by,
        changedAt: row.changed_at,
        reason: row.reason
      }));

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to get metadata history: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'HISTORY_ERROR',
        projectId,
        userId
      );
    }
  }

  /**
   * Reverts a metadata field to a previous value
   */
  async revertMetadataField(
    projectId: string,
    field: string,
    changeId: string,
    revertedBy: string,
    reason?: string
  ): Promise<void> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: revertedBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to revert metadata changes',
          'PERMISSION_DENIED',
          projectId,
          revertedBy
        );
      }

      // Get the change record
      const changeResult = await this.db.query(
        'SELECT * FROM project_metadata_changes WHERE id = $1 AND project_id = $2',
        [changeId, projectId]
      );

      if (changeResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Change record not found',
          'NOT_FOUND',
          projectId,
          revertedBy
        );
      }

      const change = changeResult.rows[0];
      const oldValue = JSON.parse(change.old_value);

      // Update the metadata field
      await this.updateMetadata(
        projectId,
        { [field]: oldValue },
        revertedBy,
        reason || `Reverted change ${changeId}`
      );

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to revert metadata field: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'REVERT_ERROR',
        projectId,
        revertedBy
      );
    }
  }
}
