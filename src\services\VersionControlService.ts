import { Pool } from 'pg';
import { Version } from '../models/Version';
import { Version as IVersion, VersionChanges, VersionDiff } from '../types';

export class VersionControlService {
  private db: Pool;

  constructor(db: Pool) {
    this.db = db;
  }

  /**
   * Creates a new version for a file
   */
  async createVersion(fileId: string, changes: VersionChanges): Promise<Version> {
    const client = await this.db.connect();
    
    try {
      await client.query('BEGIN');

      // Get the current latest version for this file
      const latestVersionResult = await client.query(
        'SELECT version FROM versions WHERE file_id = $1 ORDER BY created_at DESC LIMIT 1',
        [fileId]
      );

      // Determine the next version number
      let nextVersion = '1.0.0';
      if (latestVersionResult.rows.length > 0) {
        const currentVersion = latestVersionResult.rows[0].version;
        nextVersion = Version.getNextVersion(currentVersion, 'patch');
      }

      // Get file information for the file path
      const fileResult = await client.query(
        'SELECT name, file_type FROM files WHERE id = $1',
        [fileId]
      );

      if (fileResult.rows.length === 0) {
        throw new Error(`File with ID ${fileId} not found`);
      }

      const file = fileResult.rows[0];
      const filePath = `files/${fileId}/${nextVersion}/${file.name}`;

      // Create the new version
      const versionData: IVersion = {
        id: crypto.randomUUID(),
        fileId,
        version: nextVersion,
        createdBy: changes.modifiedBy,
        createdAt: new Date(),
        changes: changes.description,
        isLocked: false,
        isReleased: false,
        filePath
      };

      const version = new Version(versionData);

      // Insert the version into the database
      await client.query(
        `INSERT INTO versions (id, file_id, version, created_by, created_at, changes, is_locked, is_released, file_path, file_size, checksum)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          version.id,
          version.fileId,
          version.version,
          version.createdBy,
          version.createdAt,
          version.changes,
          version.isLocked,
          version.isReleased,
          version.filePath,
          0, // file_size will be updated when file is actually stored
          '' // checksum will be updated when file is actually stored
        ]
      );

      // Update the file's current version
      await client.query(
        'UPDATE files SET current_version = $1 WHERE id = $2',
        [nextVersion, fileId]
      );

      await client.query('COMMIT');
      return version;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Gets the version history for a file
   */
  async getVersionHistory(fileId: string): Promise<Version[]> {
    const result = await this.db.query(
      `SELECT v.*, u.username as created_by_username 
       FROM versions v 
       LEFT JOIN users u ON v.created_by = u.id 
       WHERE v.file_id = $1 
       ORDER BY v.created_at DESC`,
      [fileId]
    );

    return result.rows.map(row => new Version({
      id: row.id,
      fileId: row.file_id,
      version: row.version,
      createdBy: row.created_by,
      createdAt: row.created_at,
      changes: row.changes,
      isLocked: row.is_locked,
      isReleased: row.is_released,
      filePath: row.file_path
    }));
  }

  /**
   * Gets a specific version of a file
   */
  async getVersion(fileId: string, version: string): Promise<Version | null> {
    const result = await this.db.query(
      'SELECT * FROM versions WHERE file_id = $1 AND version = $2',
      [fileId, version]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return new Version({
      id: row.id,
      fileId: row.file_id,
      version: row.version,
      createdBy: row.created_by,
      createdAt: row.created_at,
      changes: row.changes,
      isLocked: row.is_locked,
      isReleased: row.is_released,
      filePath: row.file_path
    });
  }

  /**
   * Locks a specific version to prevent modifications
   */
  async lockVersion(fileId: string, version: string): Promise<void> {
    const result = await this.db.query(
      'UPDATE versions SET is_locked = true WHERE file_id = $1 AND version = $2 AND is_locked = false',
      [fileId, version]
    );

    if (result.rowCount === 0) {
      throw new Error(`Version ${version} of file ${fileId} not found or already locked`);
    }
  }

  /**
   * Unlocks a specific version (only if not released)
   */
  async unlockVersion(fileId: string, version: string): Promise<void> {
    const result = await this.db.query(
      'UPDATE versions SET is_locked = false WHERE file_id = $1 AND version = $2 AND is_released = false',
      [fileId, version]
    );

    if (result.rowCount === 0) {
      throw new Error(`Version ${version} of file ${fileId} not found, already unlocked, or is released`);
    }
  }

  /**
   * Marks a version as released (automatically locks it)
   */
  async releaseVersion(fileId: string, version: string): Promise<void> {
    const result = await this.db.query(
      'UPDATE versions SET is_released = true, is_locked = true WHERE file_id = $1 AND version = $2 AND is_released = false',
      [fileId, version]
    );

    if (result.rowCount === 0) {
      throw new Error(`Version ${version} of file ${fileId} not found or already released`);
    }
  }

  /**
   * Compares two versions of a file (basic comparison)
   */
  async compareVersions(fileId: string, version1: string, version2: string): Promise<VersionDiff> {
    const [v1, v2] = await Promise.all([
      this.getVersion(fileId, version1),
      this.getVersion(fileId, version2)
    ]);

    if (!v1 || !v2) {
      throw new Error(`One or both versions not found: ${version1}, ${version2}`);
    }

    // Basic comparison - in a real implementation, this would analyze file contents
    const diff: VersionDiff = {
      added: [],
      removed: [],
      modified: [],
      details: {
        version1: {
          version: v1.version,
          createdAt: v1.createdAt,
          createdBy: v1.createdBy,
          changes: v1.changes,
          isLocked: v1.isLocked,
          isReleased: v1.isReleased
        },
        version2: {
          version: v2.version,
          createdAt: v2.createdAt,
          createdBy: v2.createdBy,
          changes: v2.changes,
          isLocked: v2.isLocked,
          isReleased: v2.isReleased
        },
        versionComparison: Version.compareVersions(v1.version, v2.version)
      }
    };

    return diff;
  }

  /**
   * Gets the latest version for a file
   */
  async getLatestVersion(fileId: string): Promise<Version | null> {
    const result = await this.db.query(
      'SELECT * FROM versions WHERE file_id = $1 ORDER BY created_at DESC LIMIT 1',
      [fileId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return new Version({
      id: row.id,
      fileId: row.file_id,
      version: row.version,
      createdBy: row.created_by,
      createdAt: row.created_at,
      changes: row.changes,
      isLocked: row.is_locked,
      isReleased: row.is_released,
      filePath: row.file_path
    });
  }

  /**
   * Gets all released versions for a file
   */
  async getReleasedVersions(fileId: string): Promise<Version[]> {
    const result = await this.db.query(
      'SELECT * FROM versions WHERE file_id = $1 AND is_released = true ORDER BY created_at DESC',
      [fileId]
    );

    return result.rows.map(row => new Version({
      id: row.id,
      fileId: row.file_id,
      version: row.version,
      createdBy: row.created_by,
      createdAt: row.created_at,
      changes: row.changes,
      isLocked: row.is_locked,
      isReleased: row.is_released,
      filePath: row.file_path
    }));
  }

  /**
   * Checks if a version can be deleted (not referenced by other entities)
   */
  async canDeleteVersion(fileId: string, version: string): Promise<{ canDelete: boolean; reason?: string }> {
    // Check if this is the only version
    const versionCountResult = await this.db.query(
      'SELECT COUNT(*) as count FROM versions WHERE file_id = $1',
      [fileId]
    );

    const versionCount = parseInt(versionCountResult.rows[0].count);
    if (versionCount === 1) {
      return { canDelete: false, reason: 'Cannot delete the only version of a file' };
    }

    // Check if version is released
    const versionResult = await this.db.query(
      'SELECT is_released FROM versions WHERE file_id = $1 AND version = $2',
      [fileId, version]
    );

    if (versionResult.rows.length === 0) {
      return { canDelete: false, reason: 'Version not found' };
    }

    if (versionResult.rows[0].is_released) {
      return { canDelete: false, reason: 'Cannot delete a released version' };
    }

    // Check if this is the current version
    const fileResult = await this.db.query(
      'SELECT current_version FROM files WHERE id = $1',
      [fileId]
    );

    if (fileResult.rows.length > 0 && fileResult.rows[0].current_version === version) {
      return { canDelete: false, reason: 'Cannot delete the current version' };
    }

    return { canDelete: true };
  }

  /**
   * Updates version metadata (changes description) if not locked
   */
  async updateVersionChanges(fileId: string, version: string, changes: string): Promise<void> {
    const result = await this.db.query(
      'UPDATE versions SET changes = $1 WHERE file_id = $2 AND version = $3 AND is_locked = false',
      [changes, fileId, version]
    );

    if (result.rowCount === 0) {
      throw new Error(`Version ${version} of file ${fileId} not found or is locked`);
    }
  }

  /**
   * Gets version statistics for a file
   */
  async getVersionStatistics(fileId: string): Promise<{
    totalVersions: number;
    releasedVersions: number;
    lockedVersions: number;
    latestVersion: string;
    firstVersion: string;
  }> {
    const result = await this.db.query(
      `SELECT
        COUNT(*) as total_versions,
        COUNT(CASE WHEN is_released = true THEN 1 END) as released_versions,
        COUNT(CASE WHEN is_locked = true THEN 1 END) as locked_versions,
        MAX(version) as latest_version,
        MIN(version) as first_version
       FROM versions
       WHERE file_id = $1`,
      [fileId]
    );

    const row = result.rows[0];
    return {
      totalVersions: parseInt(row.total_versions),
      releasedVersions: parseInt(row.released_versions),
      lockedVersions: parseInt(row.locked_versions),
      latestVersion: row.latest_version || '1.0.0',
      firstVersion: row.first_version || '1.0.0'
    };
  }
}
