import { FileRecord as IFileRecord, Version, FileMetadata } from '../types';
export declare class FileRecord implements IFileRecord {
    id: string;
    projectId: string;
    name: string;
    originalName: string;
    fileType: 'e3series' | 'pdf' | 'dxf';
    size: number;
    checksum: string;
    uploadedBy: string;
    uploadedAt: Date;
    currentVersion: string;
    metadata: FileMetadata;
    versions: Version[];
    constructor(data: IFileRecord);
    static validate(data: Partial<IFileRecord>): {
        isValid: boolean;
        errors: string[];
    };
    static create(data: {
        projectId: string;
        name: string;
        originalName: string;
        fileType: 'e3series' | 'pdf' | 'dxf';
        size: number;
        checksum: string;
        uploadedBy: string;
        metadata?: FileMetadata;
        id?: string;
    }): FileRecord;
    updateMetadata(metadata: Partial<FileMetadata>): void;
    addVersion(version: Version): void;
    getVersion(versionNumber: string): Version | undefined;
    getLatestVersion(): Version | undefined;
    hasLockedVersions(): boolean;
    hasReleasedVersions(): boolean;
    static validateFileName(fileName: string, fileType: 'e3series' | 'pdf' | 'dxf'): boolean;
}
//# sourceMappingURL=FileRecord.d.ts.map