import { Pool } from 'pg';
import { Project } from '../models/Project';
import { 
  Project as IProject, 
  ProjectData, 
  ProjectPermission,
  User 
} from '../types';
import { ProjectManagementService as IProjectManagementService } from '../types/services';
import { AuthorizationService } from './AuthorizationService';

/**
 * Custom error class for project management operations
 */
export class ProjectManagementError extends Error {
  public readonly code: string;
  public readonly projectId?: string;
  public readonly userId?: string;

  constructor(message: string, code: string, projectId?: string, userId?: string) {
    super(message);
    this.name = 'ProjectManagementError';
    this.code = code;
    if (projectId !== undefined) {
      this.projectId = projectId;
    }
    if (userId !== undefined) {
      this.userId = userId;
    }
  }
}

/**
 * Project Management Service
 * Handles CRUD operations for projects with database persistence
 */
export class ProjectManagementService implements IProjectManagementService {
  private db: Pool;
  private authService: AuthorizationService;

  constructor(database: Pool, authorizationService: AuthorizationService) {
    this.db = database;
    this.authService = authorizationService;
  }

  /**
   * Creates a new project with proper validation and permissions
   */
  async createProject(projectData: ProjectData, createdBy: string): Promise<Project> {
    try {
      // Validate project data
      const validation = Project.validate({
        ...projectData,
        id: '',
        createdBy,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active',
        permissions: []
      });

      if (!validation.isValid) {
        throw new ProjectManagementError(
          `Invalid project data: ${validation.errors.join(', ')}`,
          'VALIDATION_ERROR'
        );
      }

      // Create project instance
      const project = Project.create({ ...projectData, createdBy });

      // Start database transaction
      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Insert project into database
        await client.query(
          `INSERT INTO projects (id, name, description, created_by, created_at, updated_at, status, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
           RETURNING *`,
          [
            project.id,
            project.name,
            project.description,
            project.createdBy,
            project.createdAt,
            project.updatedAt,
            project.status,
            JSON.stringify(project.metadata)
          ]
        );

        // Grant admin permission to creator
        await client.query(
          `INSERT INTO project_permissions (project_id, user_id, role, granted_by, granted_at)
           VALUES ($1, $2, $3, $4, $5)`,
          [project.id, createdBy, 'admin', createdBy, new Date()]
        );

        await client.query('COMMIT');

        // Add the permission to the project object
        project.addPermission({
          userId: createdBy,
          role: 'admin',
          grantedBy: createdBy
        });

        return project;

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CREATE_ERROR',
        undefined,
        createdBy
      );
    }
  }

  /**
   * Updates an existing project with validation and permission checks
   */
  async updateProject(
    projectId: string, 
    updates: Partial<ProjectData>, 
    updatedBy: string
  ): Promise<Project> {
    try {
      // Check if user has permission to update the project
      const hasPermission = await this.authService.hasPermission(
        { id: updatedBy } as User,
        'project',
        'write',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to update project',
          'PERMISSION_DENIED',
          projectId,
          updatedBy
        );
      }

      // Get existing project
      const existingProject = await this.getProject(projectId);

      // Create updated project data
      const updatedData = {
        ...existingProject,
        ...updates,
        updatedAt: new Date()
      };

      // Validate updated data
      const validation = Project.validate(updatedData);
      if (!validation.isValid) {
        throw new ProjectManagementError(
          `Invalid project update: ${validation.errors.join(', ')}`,
          'VALIDATION_ERROR',
          projectId,
          updatedBy
        );
      }

      // Update in database
      const result = await this.db.query(
        `UPDATE projects 
         SET name = $1, description = $2, metadata = $3, updated_at = $4
         WHERE id = $5 AND status != 'archived'
         RETURNING *`,
        [
          updatedData.name,
          updatedData.description,
          JSON.stringify(updatedData.metadata),
          updatedData.updatedAt,
          projectId
        ]
      );

      if (result.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found or is archived',
          'NOT_FOUND',
          projectId,
          updatedBy
        );
      }

      // Return updated project with permissions
      return await this.getProject(projectId);

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to update project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'UPDATE_ERROR',
        projectId,
        updatedBy
      );
    }
  }

  /**
   * Retrieves a project by ID with permission checks
   */
  async getProject(projectId: string, userId?: string): Promise<Project> {
    try {
      // Get project from database
      const projectResult = await this.db.query(
        'SELECT * FROM projects WHERE id = $1',
        [projectId]
      );

      if (projectResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found',
          'NOT_FOUND',
          projectId,
          userId
        );
      }

      const projectRow = projectResult.rows[0];

      // Get project permissions
      const permissionsResult = await this.db.query(
        `SELECT user_id, role, granted_by, granted_at 
         FROM project_permissions 
         WHERE project_id = $1`,
        [projectId]
      );

      const permissions: ProjectPermission[] = permissionsResult.rows.map(row => ({
        userId: row.user_id,
        role: row.role,
        grantedBy: row.granted_by,
        grantedAt: row.granted_at
      }));

      // Create project instance
      const projectData: IProject = {
        id: projectRow.id,
        name: projectRow.name,
        description: projectRow.description,
        createdBy: projectRow.created_by,
        createdAt: projectRow.created_at,
        updatedAt: projectRow.updated_at,
        status: projectRow.status,
        metadata: projectRow.metadata || {},
        permissions
      };

      return new Project(projectData);

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to get project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GET_ERROR',
        projectId,
        userId
      );
    }
  }

  /**
   * Lists projects accessible to a user
   */
  async listProjects(userId: string): Promise<Project[]> {
    try {
      // Get projects where user has permissions or is the creator
      const result = await this.db.query(
        `SELECT DISTINCT p.* 
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE p.created_by = $1 OR pp.user_id = $1
         ORDER BY p.updated_at DESC`,
        [userId]
      );

      const projects: Project[] = [];

      for (const row of result.rows) {
        // Get permissions for each project
        const permissionsResult = await this.db.query(
          `SELECT user_id, role, granted_by, granted_at 
           FROM project_permissions 
           WHERE project_id = $1`,
          [row.id]
        );

        const permissions: ProjectPermission[] = permissionsResult.rows.map(permRow => ({
          userId: permRow.user_id,
          role: permRow.role,
          grantedBy: permRow.granted_by,
          grantedAt: permRow.granted_at
        }));

        const projectData: IProject = {
          id: row.id,
          name: row.name,
          description: row.description,
          createdBy: row.created_by,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          status: row.status,
          metadata: row.metadata || {},
          permissions
        };

        projects.push(new Project(projectData));
      }

      return projects;

    } catch (error) {
      throw new ProjectManagementError(
        `Failed to list projects: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'LIST_ERROR',
        undefined,
        userId
      );
    }
  }

  /**
   * Archives a project (soft delete)
   */
  async archiveProject(projectId: string, archivedBy: string): Promise<void> {
    try {
      // Check if user has admin permission
      const hasPermission = await this.authService.hasPermission(
        { id: archivedBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to archive project',
          'PERMISSION_DENIED',
          projectId,
          archivedBy
        );
      }

      // Archive the project
      const result = await this.db.query(
        `UPDATE projects 
         SET status = 'archived', updated_at = $1
         WHERE id = $2 AND status = 'active'
         RETURNING id`,
        [new Date(), projectId]
      );

      if (result.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found or already archived',
          'NOT_FOUND',
          projectId,
          archivedBy
        );
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to archive project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ARCHIVE_ERROR',
        projectId,
        archivedBy
      );
    }
  }

  /**
   * Restores an archived project
   */
  async restoreProject(projectId: string, restoredBy: string): Promise<void> {
    try {
      // Check if user has admin permission
      const hasPermission = await this.authService.hasPermission(
        { id: restoredBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to restore project',
          'PERMISSION_DENIED',
          projectId,
          restoredBy
        );
      }

      // Restore the project
      const result = await this.db.query(
        `UPDATE projects
         SET status = 'active', updated_at = $1
         WHERE id = $2 AND status = 'archived'
         RETURNING id`,
        [new Date(), projectId]
      );

      if (result.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found or not archived',
          'NOT_FOUND',
          projectId,
          restoredBy
        );
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }

      throw new ProjectManagementError(
        `Failed to restore project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'RESTORE_ERROR',
        projectId,
        restoredBy
      );
    }
  }

  /**
   * Adds a user permission to a project
   */
  async addProjectPermission(
    projectId: string,
    userId: string,
    role: 'admin' | 'editor' | 'viewer',
    grantedBy: string
  ): Promise<void> {
    try {
      // Check if granter has admin permission
      const hasPermission = await this.authService.hasPermission(
        { id: grantedBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to grant project access',
          'PERMISSION_DENIED',
          projectId,
          grantedBy
        );
      }

      // Check if project exists and is active
      const project = await this.getProject(projectId);
      if (project.status === 'archived') {
        throw new ProjectManagementError(
          'Cannot grant permissions to archived project',
          'PROJECT_ARCHIVED',
          projectId,
          grantedBy
        );
      }

      // Add or update permission
      await this.db.query(
        `INSERT INTO project_permissions (project_id, user_id, role, granted_by, granted_at)
         VALUES ($1, $2, $3, $4, $5)
         ON CONFLICT (project_id, user_id)
         DO UPDATE SET role = $3, granted_by = $4, granted_at = $5`,
        [projectId, userId, role, grantedBy, new Date()]
      );

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }

      throw new ProjectManagementError(
        `Failed to add project permission: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PERMISSION_ERROR',
        projectId,
        grantedBy
      );
    }
  }

  /**
   * Removes a user permission from a project
   */
  async removeProjectPermission(
    projectId: string,
    userId: string,
    removedBy: string
  ): Promise<void> {
    try {
      // Check if remover has admin permission
      const hasPermission = await this.authService.hasPermission(
        { id: removedBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to remove project access',
          'PERMISSION_DENIED',
          projectId,
          removedBy
        );
      }

      // Get project to check if user is the creator
      const project = await this.getProject(projectId);
      if (project.createdBy === userId) {
        throw new ProjectManagementError(
          'Cannot remove permissions from project creator',
          'INVALID_OPERATION',
          projectId,
          removedBy
        );
      }

      // Remove permission
      const result = await this.db.query(
        'DELETE FROM project_permissions WHERE project_id = $1 AND user_id = $2',
        [projectId, userId]
      );

      if (result.rowCount === 0) {
        throw new ProjectManagementError(
          'User permission not found',
          'NOT_FOUND',
          projectId,
          removedBy
        );
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }

      throw new ProjectManagementError(
        `Failed to remove project permission: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PERMISSION_ERROR',
        projectId,
        removedBy
      );
    }
  }

  /**
   * Gets project statistics for a user
   */
  async getProjectStats(userId: string): Promise<{
    totalProjects: number;
    activeProjects: number;
    archivedProjects: number;
    ownedProjects: number;
    sharedProjects: number;
  }> {
    try {
      const result = await this.db.query(
        `SELECT
           COUNT(*) as total_projects,
           COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_projects,
           COUNT(CASE WHEN p.status = 'archived' THEN 1 END) as archived_projects,
           COUNT(CASE WHEN p.created_by = $1 THEN 1 END) as owned_projects,
           COUNT(CASE WHEN p.created_by != $1 THEN 1 END) as shared_projects
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE p.created_by = $1 OR pp.user_id = $1`,
        [userId]
      );

      const stats = result.rows[0];
      return {
        totalProjects: parseInt(stats.total_projects) || 0,
        activeProjects: parseInt(stats.active_projects) || 0,
        archivedProjects: parseInt(stats.archived_projects) || 0,
        ownedProjects: parseInt(stats.owned_projects) || 0,
        sharedProjects: parseInt(stats.shared_projects) || 0
      };

    } catch (error) {
      throw new ProjectManagementError(
        `Failed to get project statistics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STATS_ERROR',
        undefined,
        userId
      );
    }
  }

  /**
   * Searches projects by name or description
   */
  async searchProjects(
    userId: string,
    query: string,
    includeArchived: boolean = false
  ): Promise<Project[]> {
    try {
      const statusCondition = includeArchived ? '' : "AND p.status = 'active'";

      const result = await this.db.query(
        `SELECT DISTINCT p.*
         FROM projects p
         LEFT JOIN project_permissions pp ON p.id = pp.project_id
         WHERE (p.created_by = $1 OR pp.user_id = $1)
         AND (p.name ILIKE $2 OR p.description ILIKE $2)
         ${statusCondition}
         ORDER BY p.updated_at DESC`,
        [userId, `%${query}%`]
      );

      const projects: Project[] = [];

      for (const row of result.rows) {
        // Get permissions for each project
        const permissionsResult = await this.db.query(
          `SELECT user_id, role, granted_by, granted_at
           FROM project_permissions
           WHERE project_id = $1`,
          [row.id]
        );

        const permissions: ProjectPermission[] = permissionsResult.rows.map(permRow => ({
          userId: permRow.user_id,
          role: permRow.role,
          grantedBy: permRow.granted_by,
          grantedAt: permRow.granted_at
        }));

        const projectData: IProject = {
          id: row.id,
          name: row.name,
          description: row.description,
          createdBy: row.created_by,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          status: row.status,
          metadata: row.metadata || {},
          permissions
        };

        projects.push(new Project(projectData));
      }

      return projects;

    } catch (error) {
      throw new ProjectManagementError(
        `Failed to search projects: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SEARCH_ERROR',
        undefined,
        userId
      );
    }
  }
}
