{"version": 3, "file": "FileStorageService.js", "sourceRoot": "", "sources": ["../../src/services/FileStorageService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,2CAA6B;AAC7B,+CAAiC;AACjC,+BAAiC;AAGjC,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AAC1C,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AACxC,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACpC,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAChC,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAmBpC,MAAa,kBAAkB;IAG7B,YAAY,MAA+B;QACzC,IAAI,CAAC,MAAM,GAAG;YACZ,eAAe,EAAE,MAAM,EAAE,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC;YAC/E,WAAW,EAAE,MAAM,EAAE,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI;YACtD,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,IAAI;gBAC5C,UAAU,EAAE,CAAC,0BAA0B,EAAE,wBAAwB,CAAC;gBAClE,KAAK,EAAE,CAAC,iBAAiB,CAAC;gBAC1B,KAAK,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,0BAA0B,CAAC;aACxE;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CACb,SAAiB,EACjB,QAAwB,EACxB,MAAc,EACd,UAAkB,OAAO;QAGzB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,GAAG,OAAO,GAAG,aAAa,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAGjD,MAAM,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE3C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,aAAa,CAAC,QAAgB;QAC5B,OAAO,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvB,OAAO,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvB,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAKD,iBAAiB,CAAC,MAAc;QAC9B,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,QAAgB;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAKD,kBAAkB,CAAC,QAAwB,EAAE,YAAwC;QACnF,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,IAAI,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,CAAC,IAAI,yCAAyC,IAAI,CAAC,MAAM,CAAC,WAAW,SAAS,CAAC,CAAC;QACpH,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/B,CAAC;QAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC,cAAc,QAAQ,CAAC,QAAQ,2CAA2C,YAAY,GAAG,CAAC,CAAC;QAC3G,CAAC;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACxE,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAEpE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,mBAAmB,aAAa,iCAAiC,YAAY,gBAAgB,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5I,CAAC;QAGD,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClF,MAAM,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzC,QAAQ,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAE7C,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAKO,qBAAqB,CAAC,QAAoC;QAChE,MAAM,UAAU,GAAG;YACjB,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YACpC,KAAK,EAAE,CAAC,MAAM,CAAC;YACf,KAAK,EAAE,CAAC,MAAM,CAAC;SAChB,CAAC;QACF,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAKO,mBAAmB,CAAC,MAAc,EAAE,QAAoC;QAC9E,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC9B,CAAC;QAED,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;oBACzD,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAC7E,CAAC;gBACD,MAAM;YAER,KAAK,KAAK;gBACR,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC3D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACpE,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;gBAChF,CAAC;gBACD,MAAM;YAER,KAAK,UAAU;gBAEb,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBACxB,QAAQ,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;gBACzF,CAAC;gBAED,MAAM;QACV,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,gBAAwB;QAClE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,cAAc,KAAK,gBAAgB,CAAC;QAC7C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,qBAAqB,CAAC,SAAiB;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IAC3D,CAAC;IAKD,kBAAkB,CAAC,SAAiB,EAAE,MAAc;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAC3C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAA,gBAAS,EAAC,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;gBAGnC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC9C,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;QAET,CAAC;IACH,CAAC;CACF;AA7QD,gDA6QC"}