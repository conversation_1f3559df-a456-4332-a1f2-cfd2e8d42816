#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
const connection_1 = require("./connection");
const migrations_1 = require("./migrations");
async function initializeDatabase() {
    console.log('🚀 Starting database initialization...');
    try {
        const db = connection_1.DatabaseConnection.getInstance(connection_1.dbConfig);
        console.log('📡 Testing database connection...');
        const isConnected = await db.testConnection();
        if (!isConnected) {
            throw new Error('Failed to connect to database');
        }
        console.log('✅ Database connection successful');
        const migrationManager = (0, migrations_1.createMigrationManager)(db);
        console.log('📋 Checking migration status...');
        const status = await migrationManager.getStatus();
        console.log(`Applied migrations: ${status.applied.length}`);
        console.log(`Pending migrations: ${status.pending.length}`);
        if (status.pending.length > 0) {
            console.log('🔄 Running pending migrations...');
            await migrationManager.migrate();
            console.log('✅ All migrations applied successfully');
        }
        else {
            console.log('✅ Database is up to date');
        }
        const finalStatus = await migrationManager.getStatus();
        console.log('\n📊 Database Status:');
        console.log(`- Total applied migrations: ${finalStatus.applied.length}`);
        console.log(`- Pending migrations: ${finalStatus.pending.length}`);
        if (finalStatus.applied.length > 0) {
            console.log('\n📝 Applied migrations:');
            finalStatus.applied.forEach(migration => {
                console.log(`  - ${migration.id}: ${migration.name} (${migration.appliedAt?.toISOString()})`);
            });
        }
        await db.close();
        console.log('\n🎉 Database initialization completed successfully!');
    }
    catch (error) {
        console.error('❌ Database initialization failed:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    initializeDatabase();
}
//# sourceMappingURL=init.js.map