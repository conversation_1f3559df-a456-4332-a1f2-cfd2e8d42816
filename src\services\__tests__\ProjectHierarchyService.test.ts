// Mock dependencies before importing
const mockQuery = jest.fn();
const mockConnect = jest.fn();
const mockRelease = jest.fn();
const mockBegin = jest.fn();
const mockCommit = jest.fn();
const mockRollback = jest.fn();

const mockClient = {
  query: mockQuery,
  release: mockRelease
};

const mockPool = {
  query: mockQuery,
  connect: jest.fn().mockResolvedValue(mockClient)
};

const mockAuthService = {
  hasPermission: jest.fn()
};

// Mock fs operations
const mockMkdir = jest.fn();
const mockRmdir = jest.fn();
const mockAccess = jest.fn();
const mockStat = jest.fn();
const mockReaddir = jest.fn();

jest.mock('fs', () => ({
  mkdir: mockMkdir,
  rmdir: mockRmdir,
  access: mockAccess,
  stat: mockStat,
  readdir: mockReaddir
}));

jest.mock('util', () => ({
  promisify: jest.fn((fn) => {
    if (fn === mockMkdir) return mockMkdir;
    if (fn === mockRmdir) return mockRmdir;
    if (fn === mockAccess) return mockAccess;
    if (fn === mockStat) return mockStat;
    if (fn === mockReaddir) return mockReaddir;
    return fn; // Return the original function if it's already a mock
  })
}));

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-folder-uuid')
  },
  writable: true
});

import { ProjectHierarchyService } from '../ProjectHierarchyService';
import { ProjectManagementError } from '../ProjectManagementService';

describe('ProjectHierarchyService', () => {
  let service: ProjectHierarchyService;

  beforeEach(() => {
    service = new ProjectHierarchyService(
      mockPool as any,
      mockAuthService as any,
      './test-storage'
    );
    jest.clearAllMocks();
    
    // Setup default mock behaviors
    mockClient.query.mockImplementation(mockQuery);
    mockConnect.mockResolvedValue(mockClient);
    mockBegin.mockResolvedValue(undefined);
    mockCommit.mockResolvedValue(undefined);
    mockRollback.mockResolvedValue(undefined);
    mockAuthService.hasPermission.mockResolvedValue(true);
    mockMkdir.mockResolvedValue(undefined);
    mockRmdir.mockResolvedValue(undefined);
    // Don't set default for mockAccess - let each test configure it
  });

  describe('createProjectStructure', () => {
    const projectId = 'project-123';
    const createdBy = 'user-123';

    it('should create project structure successfully', async () => {
      mockQuery
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: 'folder-1' }] }) // documents
        .mockResolvedValueOnce({ rows: [{ id: 'folder-2' }] }) // schematics
        .mockResolvedValueOnce({ rows: [{ id: 'folder-3' }] }) // drawings
        .mockResolvedValueOnce({ rows: [{ id: 'folder-4' }] }) // reports
        .mockResolvedValueOnce({ rows: [{ id: 'folder-5' }] }) // archives
        .mockResolvedValueOnce(undefined); // COMMIT

      await service.createProjectStructure(projectId, createdBy);

      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('test-storage'), { recursive: true });
      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('documents'), { recursive: true });
      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('schematics'), { recursive: true });
      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('drawings'), { recursive: true });
      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('reports'), { recursive: true });
      expect(mockMkdir).toHaveBeenCalledWith(expect.stringContaining('archives'), { recursive: true });

      expect(mockQuery).toHaveBeenCalledWith('BEGIN');
      expect(mockQuery).toHaveBeenCalledWith('COMMIT');
    });

    it('should reject creation without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.createProjectStructure(projectId, createdBy))
        .rejects.toThrow(ProjectManagementError);

      expect(mockAuthService.hasPermission).toHaveBeenCalledWith(
        { id: createdBy },
        'project',
        'admin',
        projectId
      );
    });

    it('should rollback on database error', async () => {
      mockQuery
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')) // First INSERT fails
        .mockResolvedValueOnce(undefined); // ROLLBACK

      await expect(service.createProjectStructure(projectId, createdBy))
        .rejects.toThrow(ProjectManagementError);

      expect(mockQuery).toHaveBeenCalledWith('ROLLBACK');
      expect(mockRmdir).toHaveBeenCalledWith(expect.stringContaining('test-storage'), { recursive: true });
    });
  });

  describe('createFolder', () => {
    const projectId = 'project-123';
    const folderName = 'Custom Folder';
    const createdBy = 'user-123';

    it('should create a custom folder successfully', async () => {
      // Mock access to throw ENOENT (folder doesn't exist) - this happens first
      const accessError = new Error('ENOENT: no such file or directory');
      (accessError as any).code = 'ENOENT';
      mockAccess.mockRejectedValueOnce(accessError);

      // Then the database query happens
      mockQuery.mockResolvedValueOnce({
        rows: [{ id: 'folder-uuid' }]
      });

      const result = await service.createFolder(projectId, folderName, undefined, createdBy);

      expect(result.name).toBe('Custom_Folder'); // Sanitized name
      expect(result.projectId).toBe(projectId);
      expect(result.createdBy).toBe(createdBy);

      expect(mockAccess).toHaveBeenCalledWith(expect.stringContaining('Custom_Folder'));
      expect(mockMkdir).toHaveBeenCalledWith(
        expect.stringContaining('Custom_Folder'),
        { recursive: true }
      );

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO project_folders'),
        expect.arrayContaining(['test-folder-uuid', projectId, 'Custom_Folder'])
      );
    });

    it('should create folder with parent', async () => {
      const parentId = 'parent-folder-123';

      // Mock permission check
      mockAuthService.hasPermission.mockResolvedValue(true);

      // First the parent folder query happens
      mockQuery
        .mockResolvedValueOnce({
          rows: [{ path: './test-storage/project-123/documents' }]
        }) // parent folder query
        .mockResolvedValueOnce({
          rows: [{ id: 'folder-uuid' }]
        }); // insert folder

      // Then access check to see if folder exists (should not exist)
      const accessError = new Error('ENOENT: no such file or directory');
      (accessError as any).code = 'ENOENT';
      mockAccess.mockRejectedValueOnce(accessError);

      await service.createFolder(projectId, folderName, parentId, createdBy);

      expect(mockMkdir).toHaveBeenCalledWith(
        expect.stringContaining('Custom_Folder'),
        { recursive: true }
      );
    });

    it('should reject creation without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.createFolder(projectId, folderName, undefined, createdBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject empty folder name', async () => {
      await expect(service.createFolder(projectId, '', undefined, createdBy))
        .rejects.toThrow(ProjectManagementError);

      await expect(service.createFolder(projectId, '   ', undefined, createdBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject if folder already exists', async () => {
      mockAccess.mockResolvedValueOnce(undefined); // Folder exists

      await expect(service.createFolder(projectId, folderName, undefined, createdBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle parent folder not found', async () => {
      const parentId = 'nonexistent-parent';
      
      mockQuery.mockResolvedValueOnce({ rows: [] }); // Parent not found

      await expect(service.createFolder(projectId, folderName, parentId, createdBy))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('getProjectStructure', () => {
    const projectId = 'project-123';
    const userId = 'user-123';

    it('should get project structure successfully', async () => {
      const mockFolders = [
        {
          id: 'folder-1',
          project_id: projectId,
          name: 'documents',
          parent_id: null,
          path: './test-storage/project-123/documents',
          created_by: userId,
          created_at: new Date(),
          updated_at: new Date(),
          metadata: { isStandard: true }
        },
        {
          id: 'folder-2',
          project_id: projectId,
          name: 'subfolder',
          parent_id: 'folder-1',
          path: './test-storage/project-123/documents/subfolder',
          created_by: userId,
          created_at: new Date(),
          updated_at: new Date(),
          metadata: {}
        }
      ];

      // Reset mocks to ensure clean state
      jest.clearAllMocks();
      mockAuthService.hasPermission.mockResolvedValue(true);
      mockQuery.mockResolvedValueOnce({ rows: mockFolders });

      const result = await service.getProjectStructure(projectId, userId);

      expect(result).toHaveLength(2);
      expect(result[0]?.name).toBe('documents');
      expect(result[1]?.name).toBe('subfolder');
      expect(result[1]?.parentId).toBe('folder-1');
    });

    it('should reject access without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.getProjectStructure(projectId, userId))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle empty structure', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await service.getProjectStructure(projectId, userId);

      expect(result).toHaveLength(0);
    });
  });

  describe('deleteFolder', () => {
    const folderId = 'folder-123';
    const deletedBy = 'user-123';

    it('should delete a custom folder successfully', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        name: 'custom-folder',
        path: './test-storage/project-123/custom-folder',
        metadata: { isStandard: false }
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [mockFolder] }) // get folder
        .mockResolvedValueOnce({ rows: [{ count: '0' }] }) // check children
        .mockResolvedValueOnce({ rows: [{ count: '0' }] }) // check files
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: folderId }] }) // DELETE
        .mockResolvedValueOnce(undefined); // COMMIT

      await service.deleteFolder(folderId, deletedBy);

      expect(mockQuery).toHaveBeenCalledWith(
        'DELETE FROM project_folders WHERE id = $1',
        [folderId]
      );
      expect(mockRmdir).toHaveBeenCalledWith('./test-storage/project-123/custom-folder');
    });

    it('should reject deletion without permission', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        metadata: {}
      };

      mockQuery.mockResolvedValueOnce({ rows: [mockFolder] });
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.deleteFolder(folderId, deletedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject deletion of standard folders', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        metadata: { isStandard: true }
      };

      mockQuery.mockResolvedValueOnce({ rows: [mockFolder] });

      await expect(service.deleteFolder(folderId, deletedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject deletion of folder with children', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        metadata: { isStandard: false }
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [mockFolder] })
        .mockResolvedValueOnce({ rows: [{ count: '2' }] }); // Has children

      await expect(service.deleteFolder(folderId, deletedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should reject deletion of folder with files', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        metadata: {}
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [mockFolder] })
        .mockResolvedValueOnce({ rows: [{ count: '0' }] }) // No children
        .mockResolvedValueOnce({ rows: [{ count: '3' }] }); // Has files

      await expect(service.deleteFolder(folderId, deletedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle folder not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await expect(service.deleteFolder(folderId, deletedBy))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should continue on physical deletion failure', async () => {
      const mockFolder = {
        id: folderId,
        project_id: 'project-123',
        name: 'custom-folder',
        path: './test-storage/project-123/custom-folder',
        metadata: { isStandard: false }
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [mockFolder] })
        .mockResolvedValueOnce({ rows: [{ count: '0' }] })
        .mockResolvedValueOnce({ rows: [{ count: '0' }] })
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: folderId }] }) // DELETE
        .mockResolvedValueOnce(undefined); // COMMIT

      mockRmdir.mockRejectedValueOnce(new Error('Permission denied'));

      // Should not throw error even if physical deletion fails
      await service.deleteFolder(folderId, deletedBy);

      expect(mockQuery).toHaveBeenCalledWith('COMMIT');
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      mockConnect.mockRejectedValueOnce(new Error('Connection failed'));

      await expect(service.createProjectStructure('project-123', 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle file system errors', async () => {
      mockMkdir.mockRejectedValueOnce(new Error('Permission denied'));

      await expect(service.createProjectStructure('project-123', 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });
  });
});
