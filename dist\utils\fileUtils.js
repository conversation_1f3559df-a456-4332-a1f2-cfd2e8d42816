"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFileExtension = getFileExtension;
exports.getFileNameWithoutExtension = getFileNameWithoutExtension;
exports.sanitizeFileName = sanitizeFileName;
exports.generateUniqueFileName = generateUniqueFileName;
exports.calculateChecksum = calculateChecksum;
exports.formatFileSize = formatFileSize;
exports.isValidFileExtension = isValidFileExtension;
exports.getMimeTypeFromExtension = getMimeTypeFromExtension;
exports.validateFileName = validateFileName;
exports.generateStoragePath = generateStoragePath;
exports.extractMetadataFromFilename = extractMetadataFromFilename;
exports.isBinaryFile = isBinaryFile;
exports.compareVersions = compareVersions;
exports.getNextVersion = getNextVersion;
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
function getFileExtension(filename) {
    return path.extname(filename).toLowerCase();
}
function getFileNameWithoutExtension(filename) {
    return path.basename(filename, path.extname(filename));
}
function sanitizeFileName(filename) {
    return filename
        .replace(/[<>:"/\\|?*]/g, '_')
        .replace(/\s+/g, '_')
        .replace(/_{2,}/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 255);
}
function generateUniqueFileName(originalName) {
    const timestamp = Date.now();
    const extension = getFileExtension(originalName);
    const nameWithoutExt = getFileNameWithoutExtension(originalName);
    const sanitizedName = sanitizeFileName(nameWithoutExt);
    return `${sanitizedName}_${timestamp}${extension}`;
}
function calculateChecksum(buffer, algorithm = 'sha256') {
    return crypto.createHash(algorithm).update(buffer).digest('hex');
}
function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function isValidFileExtension(filename, allowedExtensions) {
    const extension = getFileExtension(filename);
    return allowedExtensions.includes(extension);
}
function getMimeTypeFromExtension(filename) {
    const extension = getFileExtension(filename);
    const mimeTypes = {
        '.pdf': 'application/pdf',
        '.dxf': 'application/dxf',
        '.e3s': 'application/x-e3series',
        '.e3p': 'application/x-e3series',
        '.e3d': 'application/x-e3series',
    };
    return mimeTypes[extension] || 'application/octet-stream';
}
function validateFileName(filename) {
    const errors = [];
    if (!filename || filename.trim().length === 0) {
        errors.push('Filename cannot be empty');
    }
    if (filename.length > 255) {
        errors.push('Filename cannot exceed 255 characters');
    }
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(filename)) {
        errors.push('Filename contains invalid characters');
    }
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
    const nameWithoutExt = getFileNameWithoutExtension(filename).toUpperCase();
    if (reservedNames.includes(nameWithoutExt)) {
        errors.push('Filename uses a reserved system name');
    }
    if (filename.startsWith(' ') || filename.endsWith(' ') || filename.startsWith('.') || filename.endsWith('.')) {
        errors.push('Filename cannot start or end with space or dot');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function generateStoragePath(projectId, fileId, version, originalName) {
    const extension = getFileExtension(originalName);
    return path.join(projectId, fileId, `${version}${extension}`).replace(/\\/g, '/');
}
function extractMetadataFromFilename(filename) {
    const metadata = {
        title: getFileNameWithoutExtension(filename)
    };
    const datePattern = /(\d{4}[-_]\d{2}[-_]\d{2})/;
    const dateMatch = filename.match(datePattern);
    if (dateMatch && dateMatch[1]) {
        const dateStr = dateMatch[1].replace(/_/g, '-');
        const parsedDate = new Date(dateStr);
        if (!isNaN(parsedDate.getTime())) {
            metadata.createdDate = parsedDate;
        }
    }
    return metadata;
}
function isBinaryFile(buffer, sampleSize = 512) {
    const sample = buffer.subarray(0, Math.min(sampleSize, buffer.length));
    for (let i = 0; i < sample.length; i++) {
        if (sample[i] === 0) {
            return true;
        }
    }
    let nonPrintableCount = 0;
    for (let i = 0; i < sample.length; i++) {
        const byte = sample[i];
        if (byte !== undefined && byte < 32 && byte !== 9 && byte !== 10 && byte !== 13) {
            nonPrintableCount++;
        }
    }
    const nonPrintableRatio = nonPrintableCount / sample.length;
    return nonPrintableRatio > 0.3;
}
function compareVersions(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    for (let i = 0; i < maxLength; i++) {
        const v1Part = v1Parts[i] || 0;
        const v2Part = v2Parts[i] || 0;
        if (v1Part > v2Part)
            return 1;
        if (v1Part < v2Part)
            return -1;
    }
    return 0;
}
function getNextVersion(currentVersion, type = 'patch') {
    const parts = currentVersion.split('.').map(Number);
    if (parts.length !== 3) {
        throw new Error('Invalid version format. Expected format: major.minor.patch');
    }
    switch (type) {
        case 'major':
            return `${(parts[0] || 0) + 1}.0.0`;
        case 'minor':
            return `${parts[0] || 0}.${(parts[1] || 0) + 1}.0`;
        case 'patch':
            return `${parts[0] || 0}.${parts[1] || 0}.${(parts[2] || 0) + 1}`;
        default:
            throw new Error('Invalid version type. Expected: major, minor, or patch');
    }
}
//# sourceMappingURL=fileUtils.js.map