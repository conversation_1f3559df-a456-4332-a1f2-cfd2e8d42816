"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbConfig = exports.getDatabase = exports.DatabaseConnection = void 0;
const pg_1 = require("pg");
const fs_1 = require("fs");
const path_1 = require("path");
class DatabaseConnection {
    constructor(config) {
        this.pool = new pg_1.Pool({
            host: config.host,
            port: config.port,
            database: config.database,
            user: config.user,
            password: config.password,
            ssl: config.ssl,
            max: config.max || 20,
            idleTimeoutMillis: config.idleTimeoutMillis || 30000,
            connectionTimeoutMillis: config.connectionTimeoutMillis || 2000,
        });
        this.pool.on('error', (err) => {
            console.error('Unexpected error on idle client', err);
            process.exit(-1);
        });
        this.pool.on('connect', (_client) => {
            console.log('New client connected to database');
        });
        this.pool.on('remove', (_client) => {
            console.log('Client removed from pool');
        });
    }
    static getInstance(config) {
        if (!DatabaseConnection.instance) {
            if (!config) {
                throw new Error('Database configuration is required for first initialization');
            }
            DatabaseConnection.instance = new DatabaseConnection(config);
        }
        return DatabaseConnection.instance;
    }
    static getDefaultConfig() {
        return {
            host: process.env['DB_HOST'] || 'localhost',
            port: parseInt(process.env['DB_PORT'] || '5432'),
            database: process.env['DB_NAME'] || 'e3_pdm_system',
            user: process.env['DB_USER'] || 'postgres',
            password: process.env['DB_PASSWORD'] || 'password',
            ssl: process.env['DB_SSL'] === 'true',
            max: parseInt(process.env['DB_MAX_CONNECTIONS'] || '20'),
            idleTimeoutMillis: parseInt(process.env['DB_IDLE_TIMEOUT'] || '30000'),
            connectionTimeoutMillis: parseInt(process.env['DB_CONNECTION_TIMEOUT'] || '2000'),
        };
    }
    async query(text, params) {
        const start = Date.now();
        try {
            const result = await this.pool.query(text, params);
            const duration = Date.now() - start;
            console.log('Executed query', { text, duration, rows: result.rowCount });
            return result;
        }
        catch (error) {
            console.error('Database query error', { text, params, error });
            throw error;
        }
    }
    getPool() {
        return this.pool;
    }
    async getClient() {
        return await this.pool.connect();
    }
    async transaction(callback) {
        const client = await this.getClient();
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async initializeSchema() {
        try {
            const schemaPath = (0, path_1.join)(__dirname, 'schema.sql');
            const schema = (0, fs_1.readFileSync)(schemaPath, 'utf8');
            const statements = schema
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);
            for (const statement of statements) {
                await this.query(statement);
            }
            console.log('Database schema initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize database schema:', error);
            throw error;
        }
    }
    async testConnection() {
        try {
            const result = await this.query('SELECT NOW() as current_time');
            console.log('Database connection test successful:', result.rows[0]);
            return true;
        }
        catch (error) {
            console.error('Database connection test failed:', error);
            return false;
        }
    }
    getPoolStats() {
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount,
        };
    }
    async close() {
        await this.pool.end();
        console.log('Database connection pool closed');
    }
}
exports.DatabaseConnection = DatabaseConnection;
const getDatabase = (config) => {
    return DatabaseConnection.getInstance(config);
};
exports.getDatabase = getDatabase;
exports.dbConfig = DatabaseConnection.getDefaultConfig();
//# sourceMappingURL=connection.js.map