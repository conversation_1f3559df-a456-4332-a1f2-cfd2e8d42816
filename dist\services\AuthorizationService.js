"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthorizationServiceImpl = void 0;
class AuthorizationServiceImpl {
    constructor(db) {
        this.db = db;
    }
    async hasPermission(user, resource, permission, resourceId) {
        if (user.role === 'admin' && resource === 'system') {
            return true;
        }
        switch (resource) {
            case 'system':
                return user.role === 'admin';
            case 'user':
                if (permission === 'read' && resourceId === user.id) {
                    return true;
                }
                return user.role === 'admin';
            case 'project':
                return await this.hasProjectPermission(user, permission, resourceId);
            case 'file':
                return await this.hasFilePermission(user, permission, resourceId);
            default:
                return false;
        }
    }
    async getProjectPermissions(userId, projectId) {
        const client = await this.db.connect();
        try {
            const query = `
        SELECT pp.user_id, pp.role, pp.granted_by, pp.granted_at, u.username
        FROM project_permissions pp
        JOIN users u ON pp.granted_by = u.id
        WHERE pp.project_id = $1 AND pp.user_id = $2
      `;
            const result = await client.query(query, [projectId, userId]);
            if (result.rows.length === 0) {
                return null;
            }
            const row = result.rows[0];
            return {
                userId: row.user_id,
                role: row.role,
                grantedBy: row.granted_by,
                grantedAt: row.granted_at
            };
        }
        finally {
            client.release();
        }
    }
    async grantProjectPermission(projectId, userId, role, grantedBy) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const existingQuery = 'SELECT id FROM project_permissions WHERE project_id = $1 AND user_id = $2';
            const existing = await client.query(existingQuery, [projectId, userId]);
            if (existing.rows.length > 0) {
                await client.query('UPDATE project_permissions SET role = $1, granted_by = $2, granted_at = CURRENT_TIMESTAMP WHERE project_id = $3 AND user_id = $4', [role, grantedBy, projectId, userId]);
            }
            else {
                await client.query('INSERT INTO project_permissions (project_id, user_id, role, granted_by) VALUES ($1, $2, $3, $4)', [projectId, userId, role, grantedBy]);
            }
            await client.query('COMMIT');
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async revokeProjectPermission(projectId, userId) {
        const client = await this.db.connect();
        try {
            await client.query('DELETE FROM project_permissions WHERE project_id = $1 AND user_id = $2', [projectId, userId]);
        }
        finally {
            client.release();
        }
    }
    async listProjectUsers(projectId) {
        const client = await this.db.connect();
        try {
            const query = `
        SELECT pp.user_id, pp.role, pp.granted_by, pp.granted_at,
               u.username, u.email
        FROM project_permissions pp
        JOIN users u ON pp.user_id = u.id
        WHERE pp.project_id = $1
        ORDER BY pp.granted_at DESC
      `;
            const result = await client.query(query, [projectId]);
            return result.rows.map(row => ({
                userId: row.user_id,
                role: row.role,
                grantedBy: row.granted_by,
                grantedAt: row.granted_at
            }));
        }
        finally {
            client.release();
        }
    }
    async hasProjectPermission(user, permission, projectId) {
        if (!projectId) {
            return false;
        }
        const client = await this.db.connect();
        try {
            const creatorQuery = 'SELECT created_by FROM projects WHERE id = $1';
            const creatorResult = await client.query(creatorQuery, [projectId]);
            if (creatorResult.rows.length > 0 && creatorResult.rows[0].created_by === user.id) {
                return true;
            }
            const permissionQuery = `
        SELECT role FROM project_permissions 
        WHERE project_id = $1 AND user_id = $2
      `;
            const result = await client.query(permissionQuery, [projectId, user.id]);
            if (result.rows.length === 0) {
                return false;
            }
            const userRole = result.rows[0].role;
            switch (permission) {
                case 'read':
                    return ['viewer', 'editor', 'admin'].includes(userRole);
                case 'write':
                    return ['editor', 'admin'].includes(userRole);
                case 'admin':
                    return userRole === 'admin';
                default:
                    return false;
            }
        }
        finally {
            client.release();
        }
    }
    async hasFilePermission(user, permission, fileId) {
        if (!fileId) {
            return false;
        }
        const client = await this.db.connect();
        try {
            const fileQuery = 'SELECT project_id FROM files WHERE id = $1';
            const fileResult = await client.query(fileQuery, [fileId]);
            if (fileResult.rows.length === 0) {
                return false;
            }
            const projectId = fileResult.rows[0].project_id;
            return await this.hasProjectPermission(user, permission, projectId);
        }
        finally {
            client.release();
        }
    }
}
exports.AuthorizationServiceImpl = AuthorizationServiceImpl;
//# sourceMappingURL=AuthorizationService.js.map