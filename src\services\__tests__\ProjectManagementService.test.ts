// Mock the database and authorization service before importing
const mockQuery = jest.fn();
const mockConnect = jest.fn();
const mockRelease = jest.fn();
const mockBegin = jest.fn();
const mockCommit = jest.fn();
const mockRollback = jest.fn();

const mockClient = {
  query: mockQuery,
  release: mockRelease
};

const mockPool = {
  query: mockQuery,
  connect: jest.fn().mockResolvedValue(mockClient)
};

const mockAuthService = {
  hasPermission: jest.fn()
};

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-uuid-123')
  },
  writable: true
});

jest.mock('pg', () => ({
  Pool: jest.fn(() => mockPool)
}));

import { ProjectManagementService, ProjectManagementError } from '../ProjectManagementService';
import { Project } from '../../models/Project';

describe('ProjectManagementService', () => {
  let service: ProjectManagementService;

  beforeEach(() => {
    service = new ProjectManagementService(mockPool as any, mockAuthService as any);
    jest.clearAllMocks();
    
    // Setup default mock behaviors
    mockClient.query.mockImplementation(mockQuery);
    mockConnect.mockResolvedValue(mockClient);
    mockBegin.mockResolvedValue(undefined);
    mockCommit.mockResolvedValue(undefined);
    mockRollback.mockResolvedValue(undefined);
  });

  describe('createProject', () => {
    const projectData = {
      name: 'Test Project',
      description: 'A test project',
      metadata: { type: 'electrical' }
    };

    it('should create a project successfully', async () => {
      mockQuery
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: 'test-uuid-123' }] }) // INSERT project
        .mockResolvedValueOnce({ rows: [{ id: 'perm-uuid' }] }) // INSERT permission
        .mockResolvedValueOnce(undefined); // COMMIT

      const result = await service.createProject(projectData, 'user-123');

      expect(result).toBeInstanceOf(Project);
      expect(result.name).toBe(projectData.name);
      expect(result.description).toBe(projectData.description);
      expect(result.createdBy).toBe('user-123');
      expect(result.status).toBe('active');

      expect(mockQuery).toHaveBeenCalledWith('BEGIN');
      expect(mockQuery).toHaveBeenCalledWith('COMMIT');
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO projects'),
        expect.arrayContaining(['test-uuid-123', 'Test Project', 'A test project', 'user-123'])
      );
    });

    it('should handle validation errors', async () => {
      const invalidData = {
        name: '', // Invalid empty name
        description: 'Test',
        metadata: {}
      };

      await expect(service.createProject(invalidData, 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should rollback on database error', async () => {
      mockQuery
        .mockResolvedValueOnce(undefined) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')) // INSERT fails
        .mockResolvedValueOnce(undefined); // ROLLBACK

      await expect(service.createProject(projectData, 'user-123'))
        .rejects.toThrow(ProjectManagementError);

      expect(mockQuery).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('updateProject', () => {
    const projectId = 'project-123';
    const updates = {
      name: 'Updated Project',
      description: 'Updated description'
    };

    beforeEach(() => {
      mockAuthService.hasPermission.mockResolvedValue(true);
    });

    it('should update a project successfully', async () => {
      // Mock getProject call
      mockQuery
        .mockResolvedValueOnce({ // getProject - project query
          rows: [{
            id: projectId,
            name: 'Original Project',
            description: 'Original description',
            created_by: 'user-123',
            created_at: new Date(),
            updated_at: new Date(),
            status: 'active',
            metadata: {}
          }]
        })
        .mockResolvedValueOnce({ // getProject - permissions query
          rows: [{
            user_id: 'user-123',
            role: 'admin',
            granted_by: 'user-123',
            granted_at: new Date()
          }]
        })
        .mockResolvedValueOnce({ // update query
          rows: [{
            id: projectId,
            name: 'Updated Project',
            description: 'Updated description'
          }]
        })
        .mockResolvedValueOnce({ // getProject after update - project query
          rows: [{
            id: projectId,
            name: 'Updated Project',
            description: 'Updated description',
            created_by: 'user-123',
            created_at: new Date(),
            updated_at: new Date(),
            status: 'active',
            metadata: {}
          }]
        })
        .mockResolvedValueOnce({ // getProject after update - permissions query
          rows: [{
            user_id: 'user-123',
            role: 'admin',
            granted_by: 'user-123',
            granted_at: new Date()
          }]
        });

      const result = await service.updateProject(projectId, updates, 'user-123');

      expect(result).toBeInstanceOf(Project);
      expect(result.name).toBe('Updated Project');
      expect(result.description).toBe('Updated description');

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE projects'),
        expect.arrayContaining(['Updated Project', 'Updated description'])
      );
    });

    it('should reject update without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.updateProject(projectId, updates, 'user-456'))
        .rejects.toThrow(ProjectManagementError);
      
      expect(mockAuthService.hasPermission).toHaveBeenCalledWith(
        { id: 'user-456' },
        'project',
        'write',
        projectId
      );
    });

    it('should handle project not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] }); // Empty result for update

      await expect(service.updateProject(projectId, updates, 'user-123'))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('getProject', () => {
    const projectId = 'project-123';

    it('should retrieve a project successfully', async () => {
      const mockProjectData = {
        id: projectId,
        name: 'Test Project',
        description: 'Test description',
        created_by: 'user-123',
        created_at: new Date(),
        updated_at: new Date(),
        status: 'active',
        metadata: { type: 'electrical' }
      };

      const mockPermissions = [{
        user_id: 'user-123',
        role: 'admin',
        granted_by: 'user-123',
        granted_at: new Date()
      }];

      mockQuery
        .mockResolvedValueOnce({ rows: [mockProjectData] })
        .mockResolvedValueOnce({ rows: mockPermissions });

      const result = await service.getProject(projectId);

      expect(result).toBeInstanceOf(Project);
      expect(result.id).toBe(projectId);
      expect(result.name).toBe('Test Project');
      expect(result.permissions).toHaveLength(1);
    });

    it('should handle project not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await expect(service.getProject(projectId))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('listProjects', () => {
    const userId = 'user-123';

    it('should list projects for a user', async () => {
      const mockProjects = [{
        id: 'project-1',
        name: 'Project 1',
        description: 'Description 1',
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date(),
        status: 'active',
        metadata: {}
      }, {
        id: 'project-2',
        name: 'Project 2',
        description: 'Description 2',
        created_by: 'other-user',
        created_at: new Date(),
        updated_at: new Date(),
        status: 'active',
        metadata: {}
      }];

      mockQuery
        .mockResolvedValueOnce({ rows: mockProjects })
        .mockResolvedValueOnce({ rows: [] }) // permissions for project-1
        .mockResolvedValueOnce({ rows: [] }); // permissions for project-2

      const result = await service.listProjects(userId);

      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(Project);
      expect(result[1]).toBeInstanceOf(Project);
    });

    it('should handle empty project list', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await service.listProjects(userId);

      expect(result).toHaveLength(0);
    });
  });

  describe('archiveProject', () => {
    const projectId = 'project-123';
    const userId = 'user-123';

    beforeEach(() => {
      mockAuthService.hasPermission.mockResolvedValue(true);
    });

    it('should archive a project successfully', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ id: projectId }]
      });

      await service.archiveProject(projectId, userId);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE projects'),
        expect.arrayContaining([expect.any(Date), projectId])
      );
    });

    it('should reject archive without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.archiveProject(projectId, userId))
        .rejects.toThrow(ProjectManagementError);
    });

    it('should handle project not found', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await expect(service.archiveProject(projectId, userId))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('restoreProject', () => {
    const projectId = 'project-123';
    const userId = 'user-123';

    beforeEach(() => {
      mockAuthService.hasPermission.mockResolvedValue(true);
    });

    it('should restore a project successfully', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ id: projectId }]
      });

      await service.restoreProject(projectId, userId);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE projects'),
        expect.arrayContaining([expect.any(Date), projectId])
      );
    });

    it('should reject restore without permission', async () => {
      mockAuthService.hasPermission.mockResolvedValue(false);

      await expect(service.restoreProject(projectId, userId))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('Permission Management', () => {
    const projectId = 'project-123';
    const userId = 'user-123';
    const targetUserId = 'user-456';

    beforeEach(() => {
      mockAuthService.hasPermission.mockResolvedValue(true);
    });

    it('should add project permission successfully', async () => {
      // Mock getProject call
      mockQuery
        .mockResolvedValueOnce({
          rows: [{
            id: projectId,
            status: 'active',
            created_by: userId
          }]
        })
        .mockResolvedValueOnce({ rows: [] }) // permissions
        .mockResolvedValueOnce({ rows: [{ id: 'perm-id' }] }); // insert permission

      await service.addProjectPermission(projectId, targetUserId, 'editor', userId);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO project_permissions'),
        expect.arrayContaining([projectId, targetUserId, 'editor', userId])
      );
    });

    it('should remove project permission successfully', async () => {
      // Mock getProject call
      mockQuery
        .mockResolvedValueOnce({
          rows: [{
            id: projectId,
            created_by: 'other-user' // Different from targetUserId
          }]
        })
        .mockResolvedValueOnce({ rows: [] }) // permissions
        .mockResolvedValueOnce({ rowCount: 1 }); // delete permission

      await service.removeProjectPermission(projectId, targetUserId, userId);

      expect(mockQuery).toHaveBeenCalledWith(
        'DELETE FROM project_permissions WHERE project_id = $1 AND user_id = $2',
        [projectId, targetUserId]
      );
    });

    it('should prevent removing creator permissions', async () => {
      // Mock getProject call - creator is same as target user
      mockQuery
        .mockResolvedValueOnce({
          rows: [{
            id: projectId,
            created_by: targetUserId // Same as target user
          }]
        })
        .mockResolvedValueOnce({ rows: [] }); // permissions

      await expect(service.removeProjectPermission(projectId, targetUserId, userId))
        .rejects.toThrow(ProjectManagementError);
    });
  });

  describe('Project Statistics', () => {
    const userId = 'user-123';

    it('should get project statistics', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{
          total_projects: '5',
          active_projects: '3',
          archived_projects: '2',
          owned_projects: '4',
          shared_projects: '1'
        }]
      });

      const stats = await service.getProjectStats(userId);

      expect(stats).toEqual({
        totalProjects: 5,
        activeProjects: 3,
        archivedProjects: 2,
        ownedProjects: 4,
        sharedProjects: 1
      });
    });
  });

  describe('Search Projects', () => {
    const userId = 'user-123';
    const query = 'test';

    it('should search projects successfully', async () => {
      const mockProjects = [{
        id: 'project-1',
        name: 'Test Project',
        description: 'A test project',
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date(),
        status: 'active',
        metadata: {}
      }];

      mockQuery
        .mockResolvedValueOnce({ rows: mockProjects })
        .mockResolvedValueOnce({ rows: [] }); // permissions

      const result = await service.searchProjects(userId, query);

      expect(result).toHaveLength(1);
      expect(result[0]?.name).toBe('Test Project');
    });

    it('should include archived projects when requested', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [] })
        .mockResolvedValueOnce({ rows: [] });

      await service.searchProjects(userId, query, true);

      expect(mockQuery).toHaveBeenCalledWith(
        expect.not.stringContaining("AND p.status = 'active'"),
        expect.any(Array)
      );
    });
  });
});
