import { Pool } from 'pg';
import { User, ProjectPermission } from '../types';
export type Permission = 'read' | 'write' | 'admin';
export type Resource = 'project' | 'file' | 'user' | 'system';
export interface AuthorizationService {
    hasPermission(user: User, resource: Resource, permission: Permission, resourceId?: string): Promise<boolean>;
    getProjectPermissions(userId: string, projectId: string): Promise<ProjectPermission | null>;
    grantProjectPermission(projectId: string, userId: string, role: 'admin' | 'editor' | 'viewer', grantedBy: string): Promise<void>;
    revokeProjectPermission(projectId: string, userId: string): Promise<void>;
    listProjectUsers(projectId: string): Promise<ProjectPermission[]>;
}
export declare class AuthorizationServiceImpl implements AuthorizationService {
    private db;
    constructor(db: Pool);
    hasPermission(user: User, resource: Resource, permission: Permission, resourceId?: string): Promise<boolean>;
    getProjectPermissions(userId: string, projectId: string): Promise<ProjectPermission | null>;
    grantProjectPermission(projectId: string, userId: string, role: 'admin' | 'editor' | 'viewer', grantedBy: string): Promise<void>;
    revokeProjectPermission(projectId: string, userId: string): Promise<void>;
    listProjectUsers(projectId: string): Promise<ProjectPermission[]>;
    private hasProjectPermission;
    private hasFilePermission;
}
//# sourceMappingURL=AuthorizationService.d.ts.map