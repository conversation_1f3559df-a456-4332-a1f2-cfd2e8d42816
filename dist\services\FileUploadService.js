"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadService = void 0;
const multer_1 = __importDefault(require("multer"));
const uuid_1 = require("uuid");
class FileUploadService {
    constructor(fileStorageService, config) {
        this.fileStorageService = fileStorageService;
        this.uploadConfig = {
            maxFileSize: config?.maxFileSize || 1024 * 1024 * 1024,
            allowedFileTypes: config?.allowedFileTypes || ['e3series', 'pdf', 'dxf'],
            tempStoragePath: config?.tempStoragePath
        };
    }
    createUploadMiddleware() {
        const storage = multer_1.default.memoryStorage();
        return (0, multer_1.default)({
            storage,
            limits: {
                fileSize: this.uploadConfig.maxFileSize,
                files: 1
            },
            fileFilter: (req, file, cb) => {
                const isValidFile = this.isValidFileType(file);
                if (isValidFile) {
                    cb(null, true);
                }
                else {
                    cb(new Error(`Invalid file type. Allowed types: ${this.uploadConfig.allowedFileTypes.join(', ')}`));
                }
            }
        });
    }
    async processUpload(file, projectId, uploadedBy, fileType, metadata) {
        if (!file) {
            throw new Error('No file provided');
        }
        const fileData = {
            buffer: file.buffer,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size
        };
        const validation = this.fileStorageService.validateFileFormat(fileData, fileType);
        if (!validation.isValid) {
            throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
        }
        const fileId = (0, uuid_1.v4)();
        const checksum = this.fileStorageService.calculateChecksum(fileData.buffer);
        const filePath = await this.fileStorageService.storeFile(projectId, fileData, fileId);
        const fileRecord = types_1.FileRecord.create({
            id: fileId,
            projectId,
            name: this.sanitizeFileName(file.originalname),
            originalName: file.originalname,
            fileType,
            size: file.size,
            checksum,
            uploadedBy,
            metadata: metadata || {}
        });
        return fileRecord;
    }
    isValidFileType(file) {
        const fileExtension = file.originalname.toLowerCase().split('.').pop();
        const typeExtensionMap = {
            'e3series': ['e3s', 'e3p', 'e3d'],
            'pdf': ['pdf'],
            'dxf': ['dxf']
        };
        return this.uploadConfig.allowedFileTypes.some(type => {
            const validExtensions = typeExtensionMap[type];
            return validExtensions.includes(fileExtension || '');
        });
    }
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[^a-zA-Z0-9.-]/g, '_')
            .replace(/_{2,}/g, '_')
            .replace(/^_+|_+$/g, '')
            .substring(0, 255);
    }
    getFileTypeFromExtension(fileName) {
        const extension = fileName.toLowerCase().split('.').pop();
        if (['e3s', 'e3p', 'e3d'].includes(extension || '')) {
            return 'e3series';
        }
        if (extension === 'pdf') {
            return 'pdf';
        }
        if (extension === 'dxf') {
            return 'dxf';
        }
        return null;
    }
    async validateFileBeforeUpload(file) {
        const errors = [];
        if (file.size > this.uploadConfig.maxFileSize) {
            errors.push(`File size exceeds maximum allowed size of ${this.uploadConfig.maxFileSize} bytes`);
        }
        if (file.size === 0) {
            errors.push('File is empty');
        }
        if (!this.isValidFileType(file)) {
            errors.push(`Invalid file type. Allowed types: ${this.uploadConfig.allowedFileTypes.join(', ')}`);
        }
        if (!file.originalname || file.originalname.trim().length === 0) {
            errors.push('File name is required');
        }
        if (file.originalname && file.originalname.length > 255) {
            errors.push('File name is too long (maximum 255 characters)');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.FileUploadService = FileUploadService;
//# sourceMappingURL=FileUploadService.js.map