import { Pool } from 'pg';
import { User } from '../types';
import { AuthenticationService as IAuthenticationService } from '../types/services';
export declare class AuthenticationService implements IAuthenticationService {
    private db;
    private jwtSecret;
    private jwtExpiresIn;
    private refreshTokens;
    constructor(db: Pool, jwtSecret: string, jwtExpiresIn?: string);
    authenticate(username: string, password: string): Promise<{
        user: User;
        token: string;
    }>;
    validateToken(token: string): Promise<User>;
    refreshToken(token: string): Promise<string>;
    logout(token: string): Promise<void>;
    createUser(username: string, email: string, password: string, role?: 'admin' | 'user'): Promise<User>;
    changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;
    private generateToken;
}
//# sourceMappingURL=AuthenticationService.d.ts.map