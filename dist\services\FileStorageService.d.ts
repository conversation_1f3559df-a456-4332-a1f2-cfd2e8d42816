import * as fs from 'fs';
import { FileValidationResult } from '../types';
export interface FileUploadData {
    buffer: Buffer;
    originalName: string;
    mimetype: string;
    size: number;
}
export interface StorageConfig {
    baseStoragePath: string;
    maxFileSize: number;
    allowedMimeTypes: {
        'e3series': string[];
        'pdf': string[];
        'dxf': string[];
    };
}
export declare class FileStorageService {
    private config;
    constructor(config?: Partial<StorageConfig>);
    initialize(): Promise<void>;
    storeFile(projectId: string, fileData: FileUploadData, fileId: string, version?: string): Promise<string>;
    getFileStream(filePath: string): NodeJS.ReadableStream;
    retrieveFile(filePath: string): Promise<Buffer>;
    deleteFile(filePath: string): Promise<void>;
    fileExists(filePath: string): Promise<boolean>;
    getFileStats(filePath: string): Promise<fs.Stats>;
    calculateChecksum(buffer: Buffer): string;
    calculateChecksumFromFile(filePath: string): Promise<string>;
    validateFileFormat(fileData: FileUploadData, expectedType: 'e3series' | 'pdf' | 'dxf'): FileValidationResult;
    private getExpectedExtensions;
    private validateFileContent;
    verifyFileIntegrity(filePath: string, expectedChecksum: string): Promise<boolean>;
    getProjectStoragePath(projectId: string): string;
    getFileStoragePath(projectId: string, fileId: string): string;
    cleanupEmptyDirectories(dirPath: string): Promise<void>;
}
//# sourceMappingURL=FileStorageService.d.ts.map