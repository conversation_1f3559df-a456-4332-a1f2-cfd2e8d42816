"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectMetadataService = void 0;
const ProjectManagementService_1 = require("./ProjectManagementService");
class ProjectMetadataService {
    constructor(database, authorizationService) {
        this.db = database;
        this.authService = authorizationService;
        this.validationRules = new Map();
        this.initializeDefaultValidationRules();
    }
    initializeDefaultValidationRules() {
        const defaultRules = [
            {
                field: 'projectType',
                type: 'string',
                allowedValues: ['electrical', 'mechanical', 'software', 'mixed']
            },
            {
                field: 'priority',
                type: 'string',
                allowedValues: ['low', 'medium', 'high', 'critical']
            },
            {
                field: 'budget',
                type: 'number',
                min: 0
            },
            {
                field: 'deadline',
                type: 'date'
            },
            {
                field: 'tags',
                type: 'array'
            },
            {
                field: 'customFields',
                type: 'object'
            },
            {
                field: 'version',
                type: 'string',
                pattern: /^\d+\.\d+(\.\d+)?$/
            },
            {
                field: 'department',
                type: 'string',
                minLength: 2,
                maxLength: 50
            },
            {
                field: 'isConfidential',
                type: 'boolean'
            }
        ];
        this.validationRules.set('default', defaultRules);
    }
    addValidationRules(projectType, rules) {
        this.validationRules.set(projectType, rules);
    }
    validateMetadata(metadata, projectType = 'default') {
        const errors = [];
        const rules = this.validationRules.get(projectType) || this.validationRules.get('default') || [];
        for (const rule of rules) {
            const value = metadata[rule.field];
            if (rule.required && (value === undefined || value === null || value === '')) {
                errors.push(`Field '${rule.field}' is required`);
                continue;
            }
            if (value === undefined || value === null) {
                continue;
            }
            if (!this.validateType(value, rule.type)) {
                errors.push(`Field '${rule.field}' must be of type ${rule.type}`);
                continue;
            }
            if (rule.type === 'string' && typeof value === 'string') {
                if (rule.minLength && value.length < rule.minLength) {
                    errors.push(`Field '${rule.field}' must be at least ${rule.minLength} characters long`);
                }
                if (rule.maxLength && value.length > rule.maxLength) {
                    errors.push(`Field '${rule.field}' must be no more than ${rule.maxLength} characters long`);
                }
                if (rule.pattern && !rule.pattern.test(value)) {
                    errors.push(`Field '${rule.field}' does not match required pattern`);
                }
            }
            if (rule.type === 'number' && typeof value === 'number') {
                if (rule.min !== undefined && value < rule.min) {
                    errors.push(`Field '${rule.field}' must be at least ${rule.min}`);
                }
                if (rule.max !== undefined && value > rule.max) {
                    errors.push(`Field '${rule.field}' must be no more than ${rule.max}`);
                }
            }
            if (rule.allowedValues && !rule.allowedValues.includes(value)) {
                errors.push(`Field '${rule.field}' must be one of: ${rule.allowedValues.join(', ')}`);
            }
            if (rule.customValidator) {
                const customResult = rule.customValidator(value);
                if (!customResult.isValid) {
                    errors.push(customResult.error || `Field '${rule.field}' failed custom validation`);
                }
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    validateType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'date':
                return value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)));
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
    async updateMetadata(projectId, metadataUpdates, updatedBy, reason) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: updatedBy }, 'project', 'write', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to update project metadata', 'PERMISSION_DENIED', projectId, updatedBy);
            }
            const projectResult = await this.db.query('SELECT metadata FROM projects WHERE id = $1', [projectId]);
            if (projectResult.rows.length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Project not found', 'NOT_FOUND', projectId, updatedBy);
            }
            const currentMetadata = projectResult.rows[0].metadata || {};
            const newMetadata = { ...currentMetadata, ...metadataUpdates };
            const validation = this.validateMetadata(newMetadata);
            if (!validation.isValid) {
                throw new ProjectManagementService_1.ProjectManagementError(`Metadata validation failed: ${validation.errors.join(', ')}`, 'VALIDATION_ERROR', projectId, updatedBy);
            }
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                await client.query('UPDATE projects SET metadata = $1, updated_at = $2 WHERE id = $3', [JSON.stringify(newMetadata), new Date(), projectId]);
                for (const [field, newValue] of Object.entries(metadataUpdates)) {
                    const oldValue = currentMetadata[field];
                    if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
                        await client.query(`INSERT INTO project_metadata_changes 
               (id, project_id, field, old_value, new_value, changed_by, changed_at, reason)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`, [
                            crypto.randomUUID(),
                            projectId,
                            field,
                            JSON.stringify(oldValue),
                            JSON.stringify(newValue),
                            updatedBy,
                            new Date(),
                            reason || null
                        ]);
                    }
                }
                await client.query('COMMIT');
            }
            catch (error) {
                await client.query('ROLLBACK');
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to update metadata: ${error instanceof Error ? error.message : 'Unknown error'}`, 'UPDATE_ERROR', projectId, updatedBy);
        }
    }
    async getMetadataHistory(projectId, userId, limit = 50) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: userId }, 'project', 'read', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to view metadata history', 'PERMISSION_DENIED', projectId, userId);
            }
            const result = await this.db.query(`SELECT id, project_id, field, old_value, new_value, changed_by, changed_at, reason
         FROM project_metadata_changes 
         WHERE project_id = $1 
         ORDER BY changed_at DESC 
         LIMIT $2`, [projectId, limit]);
            return result.rows.map(row => ({
                id: row.id,
                projectId: row.project_id,
                field: row.field,
                oldValue: JSON.parse(row.old_value),
                newValue: JSON.parse(row.new_value),
                changedBy: row.changed_by,
                changedAt: row.changed_at,
                reason: row.reason
            }));
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to get metadata history: ${error instanceof Error ? error.message : 'Unknown error'}`, 'HISTORY_ERROR', projectId, userId);
        }
    }
    async revertMetadataField(projectId, field, changeId, revertedBy, reason) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: revertedBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to revert metadata changes', 'PERMISSION_DENIED', projectId, revertedBy);
            }
            const changeResult = await this.db.query('SELECT * FROM project_metadata_changes WHERE id = $1 AND project_id = $2', [changeId, projectId]);
            if (changeResult.rows.length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Change record not found', 'NOT_FOUND', projectId, revertedBy);
            }
            const change = changeResult.rows[0];
            const oldValue = JSON.parse(change.old_value);
            await this.updateMetadata(projectId, { [field]: oldValue }, revertedBy, reason || `Reverted change ${changeId}`);
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to revert metadata field: ${error instanceof Error ? error.message : 'Unknown error'}`, 'REVERT_ERROR', projectId, revertedBy);
        }
    }
}
exports.ProjectMetadataService = ProjectMetadataService;
//# sourceMappingURL=ProjectMetadataService.js.map