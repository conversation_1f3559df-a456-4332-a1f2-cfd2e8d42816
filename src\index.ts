import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { getDatabase } from './database';
import { createAuthRoutes, createProjectRoutes, createFileRoutes, createVersionRoutes } from './routes';

const app = express();
const PORT = process.env['PORT'] || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Initialize database and routes
async function initializeApp() {
  try {
    const dbConnection = getDatabase();
    const db = dbConnection.getPool();

    // API routes
    app.use('/api/auth', createAuthRoutes(db));
    app.use('/api/projects', createProjectRoutes(db));
    app.use('/api/files', createFileRoutes(db));
    app.use('/api/versions', createVersionRoutes(db));

    console.log('API routes initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database and routes:', error);
    process.exit(1);
  }
}

// Initialize the app
initializeApp();

// Error handling middleware
app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`E3 PDM System server running on port ${PORT}`);
});

export default app;