"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMigrationManager = exports.MigrationManager = exports.dbConfig = exports.getDatabase = exports.DatabaseConnection = void 0;
var connection_1 = require("./connection");
Object.defineProperty(exports, "DatabaseConnection", { enumerable: true, get: function () { return connection_1.DatabaseConnection; } });
Object.defineProperty(exports, "getDatabase", { enumerable: true, get: function () { return connection_1.getDatabase; } });
Object.defineProperty(exports, "dbConfig", { enumerable: true, get: function () { return connection_1.dbConfig; } });
var migrations_1 = require("./migrations");
Object.defineProperty(exports, "MigrationManager", { enumerable: true, get: function () { return migrations_1.MigrationManager; } });
Object.defineProperty(exports, "createMigrationManager", { enumerable: true, get: function () { return migrations_1.createMigrationManager; } });
//# sourceMappingURL=index.js.map