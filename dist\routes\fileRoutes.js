"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFileRoutes = createFileRoutes;
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const FileManagementService_1 = require("../services/FileManagementService");
const FileStorageService_1 = require("../services/FileStorageService");
const FileUploadService_1 = require("../services/FileUploadService");
const VersionControlService_1 = require("../services/VersionControlService");
const FileParserService_1 = require("../services/FileParserService");
const auth_1 = require("../middleware/auth");
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 1024 * 1024 * 1024,
    },
    fileFilter: (_req, file, cb) => {
        const allowedMimeTypes = [
            'application/pdf',
            'application/dxf',
            'image/vnd.dxf',
            'application/octet-stream',
            'application/x-e3series'
        ];
        if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error('Unsupported file type'));
        }
    }
});
function createFileRoutes(db) {
    const router = (0, express_1.Router)();
    const fileStorageService = new FileStorageService_1.FileStorageService();
    const fileUploadService = new FileUploadService_1.FileUploadService(fileStorageService);
    const versionControlService = new VersionControlService_1.VersionControlService(db);
    const fileParserService = new FileParserService_1.FileParserService();
    const fileService = new FileManagementService_1.FileManagementService(fileStorageService, fileUploadService, versionControlService, fileParserService, db);
    router.get('/', auth_1.auth, async (req, res) => {
        try {
            const { projectId, fileType, dateFrom, dateTo, author } = req.query;
            if (!projectId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_PROJECT_ID',
                        message: 'Project ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const filters = {};
            if (fileType)
                filters.fileType = fileType;
            if (dateFrom)
                filters.dateFrom = new Date(dateFrom);
            if (dateTo)
                filters.dateTo = new Date(dateTo);
            if (author)
                filters.author = author;
            const files = await fileService.listFiles(projectId, filters);
            res.json({
                files: files.map(file => ({
                    id: file.id,
                    name: file.name,
                    originalName: file.originalName,
                    fileType: file.fileType,
                    size: file.size,
                    uploadedBy: file.uploadedBy,
                    uploadedAt: file.uploadedAt,
                    currentVersion: file.currentVersion,
                    metadata: file.metadata,
                    versionCount: file.versions.length
                }))
            });
        }
        catch (error) {
            console.error('List files error:', error);
            res.status(500).json({
                error: {
                    code: 'LIST_FILES_FAILED',
                    message: 'Failed to list files',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.post('/upload', auth_1.auth, upload.single('file'), async (req, res) => {
        try {
            const user = req.user;
            const file = req.file;
            const { projectId, fileType, metadata } = req.body;
            if (!file) {
                return res.status(400).json({
                    error: {
                        code: 'NO_FILE_PROVIDED',
                        message: 'No file provided',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (!projectId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_PROJECT_ID',
                        message: 'Project ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (!fileType || !['e3series', 'pdf', 'dxf'].includes(fileType)) {
                return res.status(400).json({
                    error: {
                        code: 'INVALID_FILE_TYPE',
                        message: 'File type must be one of: e3series, pdf, dxf',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const validation = await fileService.validateFile(file);
            if (!validation.isValid) {
                return res.status(400).json({
                    error: {
                        code: 'FILE_VALIDATION_FAILED',
                        message: 'File validation failed',
                        details: validation.errors,
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const parsedMetadata = metadata ? JSON.parse(metadata) : {};
            const fileRecord = await fileService.uploadFile(file, projectId, user.id, fileType, parsedMetadata);
            res.status(201).json({
                file: {
                    id: fileRecord.id,
                    name: fileRecord.name,
                    originalName: fileRecord.originalName,
                    fileType: fileRecord.fileType,
                    size: fileRecord.size,
                    uploadedBy: fileRecord.uploadedBy,
                    uploadedAt: fileRecord.uploadedAt,
                    currentVersion: fileRecord.currentVersion,
                    metadata: fileRecord.metadata
                },
                warnings: validation.warnings
            });
        }
        catch (error) {
            console.error('Upload file error:', error);
            res.status(500).json({
                error: {
                    code: 'UPLOAD_FAILED',
                    message: 'Failed to upload file',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/:fileId', auth_1.auth, async (req, res) => {
        try {
            const { fileId } = req.params;
            if (!fileId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_FILE_ID',
                        message: 'File ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const file = await fileService.getFile(fileId);
            if (!file) {
                return res.status(404).json({
                    error: {
                        code: 'FILE_NOT_FOUND',
                        message: 'File not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            res.json({
                file: {
                    id: file.id,
                    projectId: file.projectId,
                    name: file.name,
                    originalName: file.originalName,
                    fileType: file.fileType,
                    size: file.size,
                    checksum: file.checksum,
                    uploadedBy: file.uploadedBy,
                    uploadedAt: file.uploadedAt,
                    currentVersion: file.currentVersion,
                    metadata: file.metadata,
                    versions: file.versions.map(v => ({
                        id: v.id,
                        version: v.version,
                        createdAt: v.createdAt,
                        createdBy: v.createdBy,
                        changes: v.changes,
                        isLocked: v.isLocked,
                        isReleased: v.isReleased
                    }))
                }
            });
        }
        catch (error) {
            console.error('Get file error:', error);
            res.status(500).json({
                error: {
                    code: 'GET_FILE_FAILED',
                    message: 'Failed to get file',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.get('/:fileId/download', auth_1.auth, async (req, res) => {
        try {
            const { fileId } = req.params;
            const { version } = req.query;
            if (!fileId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_FILE_ID',
                        message: 'File ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const file = await fileService.getFile(fileId);
            if (!file) {
                return res.status(404).json({
                    error: {
                        code: 'FILE_NOT_FOUND',
                        message: 'File not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const fileStream = await fileService.downloadFile(fileId, version);
            res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
            res.setHeader('Content-Type', 'application/octet-stream');
            fileStream.pipe(res);
        }
        catch (error) {
            console.error('Download file error:', error);
            res.status(500).json({
                error: {
                    code: 'DOWNLOAD_FAILED',
                    message: 'Failed to download file',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    router.delete('/:fileId', auth_1.auth, async (req, res) => {
        try {
            const { fileId } = req.params;
            if (!fileId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_FILE_ID',
                        message: 'File ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            await fileService.deleteFile(fileId);
            res.json({
                message: 'File deleted successfully'
            });
        }
        catch (error) {
            console.error('Delete file error:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({
                    error: {
                        code: 'FILE_NOT_FOUND',
                        message: 'File not found',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            else {
                res.status(500).json({
                    error: {
                        code: 'DELETE_FAILED',
                        message: 'Failed to delete file',
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
    });
    router.post('/:fileId/versions', auth_1.auth, upload.single('file'), async (req, res) => {
        try {
            const { fileId } = req.params;
            const user = req.user;
            const file = req.file;
            const { changes } = req.body;
            if (!file) {
                return res.status(400).json({
                    error: {
                        code: 'NO_FILE_PROVIDED',
                        message: 'No file provided',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (!changes) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_CHANGES',
                        message: 'Changes description is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            if (!fileId) {
                return res.status(400).json({
                    error: {
                        code: 'MISSING_FILE_ID',
                        message: 'File ID is required',
                        timestamp: new Date().toISOString()
                    }
                });
            }
            const fileRecord = await fileService.createNewVersion(fileId, file, changes, user.id);
            res.status(201).json({
                file: {
                    id: fileRecord.id,
                    currentVersion: fileRecord.currentVersion,
                    metadata: fileRecord.metadata
                }
            });
        }
        catch (error) {
            console.error('Create version error:', error);
            res.status(500).json({
                error: {
                    code: 'CREATE_VERSION_FAILED',
                    message: 'Failed to create new version',
                    timestamp: new Date().toISOString()
                }
            });
        }
    });
    return router;
}
//# sourceMappingURL=fileRoutes.js.map