{"version": 3, "file": "FileParserService.d.ts", "sourceRoot": "", "sources": ["../../src/services/FileParserService.ts"], "names": [], "mappings": "AAIA,OAAO,EACL,aAAa,EACb,WAAW,EACX,WAAW,EACX,oBAAoB,EAKrB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,iBAAiB,IAAI,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAQ5E,qBAAa,gBAAiB,SAAQ,KAAK;IACzC,SAAgB,QAAQ,EAAE,MAAM,CAAC;IACjC,SAAgB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClC,SAAgB,WAAW,EAAE,MAAM,EAAE,CAAC;gBAE1B,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,WAAW,GAAE,MAAM,EAAO;CAS7F;AAED,qBAAa,iBAAkB,YAAW,kBAAkB;IAMpD,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;IAqBrD,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IA6IxD,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IAgGxD,kBAAkB,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO,CAAC,oBAAoB,CAAC;IA0DnE,OAAO,CAAC,iBAAiB;IAiGzB,OAAO,CAAC,kBAAkB;IA4B1B,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,eAAe;IAWvB,OAAO,CAAC,sBAAsB;IAmC9B,OAAO,CAAC,WAAW;YA+BL,iBAAiB;YAwCjB,kBAAkB;YA6BlB,kBAAkB;IA2BhC,OAAO,CAAC,uBAAuB;IAU/B,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,4BAA4B;YAgCtB,eAAe;CAqC9B"}