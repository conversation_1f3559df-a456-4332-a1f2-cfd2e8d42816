"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const database_1 = require("./database");
const routes_1 = require("./routes");
const app = (0, express_1.default)();
const PORT = process.env['PORT'] || 3000;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
app.get('/health', (_req, res) => {
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});
async function initializeApp() {
    try {
        const dbConnection = (0, database_1.getDatabase)();
        const db = dbConnection.getPool();
        app.use('/api/auth', (0, routes_1.createAuthRoutes)(db));
        app.use('/api/projects', (0, routes_1.createProjectRoutes)(db));
        app.use('/api/files', (0, routes_1.createFileRoutes)(db));
        app.use('/api/versions', (0, routes_1.createVersionRoutes)(db));
        console.log('API routes initialized successfully');
    }
    catch (error) {
        console.error('Failed to initialize database and routes:', error);
        process.exit(1);
    }
}
initializeApp();
app.use((err, _req, res, _next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Internal server error' });
});
app.use('*', (_req, res) => {
    res.status(404).json({ error: 'Route not found' });
});
app.listen(PORT, () => {
    console.log(`E3 PDM System server running on port ${PORT}`);
});
exports.default = app;
//# sourceMappingURL=index.js.map