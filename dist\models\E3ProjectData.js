"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.E3ProjectData = void 0;
class E3ProjectData {
    constructor(data) {
        this.components = data.components;
        this.connections = data.connections;
        this.properties = data.properties;
        this.layers = data.layers;
        this.sheets = data.sheets;
    }
    static validate(data) {
        const errors = [];
        if (!data.components || !Array.isArray(data.components)) {
            errors.push('Components must be an array');
        }
        if (!data.connections || !Array.isArray(data.connections)) {
            errors.push('Connections must be an array');
        }
        if (!data.properties || typeof data.properties !== 'object') {
            errors.push('Properties must be an object');
        }
        if (!data.layers || !Array.isArray(data.layers)) {
            errors.push('Layers must be an array');
        }
        if (!data.sheets || !Array.isArray(data.sheets)) {
            errors.push('Sheets must be an array');
        }
        if (data.components && Array.isArray(data.components)) {
            data.components.forEach((component, index) => {
                const componentErrors = E3ProjectData.validateComponent(component);
                componentErrors.forEach(error => {
                    errors.push(`Component ${index}: ${error}`);
                });
            });
        }
        if (data.connections && Array.isArray(data.connections)) {
            data.connections.forEach((connection, index) => {
                const connectionErrors = E3ProjectData.validateConnection(connection);
                connectionErrors.forEach(error => {
                    errors.push(`Connection ${index}: ${error}`);
                });
            });
        }
        if (data.layers && Array.isArray(data.layers)) {
            data.layers.forEach((layer, index) => {
                const layerErrors = E3ProjectData.validateLayer(layer);
                layerErrors.forEach(error => {
                    errors.push(`Layer ${index}: ${error}`);
                });
            });
        }
        if (data.sheets && Array.isArray(data.sheets)) {
            data.sheets.forEach((sheet, index) => {
                const sheetErrors = E3ProjectData.validateSheet(sheet);
                sheetErrors.forEach(error => {
                    errors.push(`Sheet ${index}: ${error}`);
                });
            });
        }
        if (data.components && Array.isArray(data.components) && data.connections && Array.isArray(data.connections)) {
            const componentIds = new Set(data.components.map(c => c.id));
            data.connections.forEach((connection, index) => {
                if (!componentIds.has(connection.fromComponent)) {
                    errors.push(`Connection ${index}: fromComponent "${connection.fromComponent}" does not exist`);
                }
                if (!componentIds.has(connection.toComponent)) {
                    errors.push(`Connection ${index}: toComponent "${connection.toComponent}" does not exist`);
                }
            });
        }
        if (data.components && Array.isArray(data.components) && data.sheets && Array.isArray(data.sheets)) {
            const componentIds = new Set(data.components.map(c => c.id));
            data.sheets.forEach((sheet, index) => {
                if (sheet.components && Array.isArray(sheet.components)) {
                    sheet.components.forEach((componentId, compIndex) => {
                        if (!componentIds.has(componentId)) {
                            errors.push(`Sheet ${index}, component ${compIndex}: component "${componentId}" does not exist`);
                        }
                    });
                }
            });
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    static validateComponent(component) {
        const errors = [];
        if (!component.id || typeof component.id !== 'string' || component.id.trim().length === 0) {
            errors.push('id is required and must be a non-empty string');
        }
        if (!component.name || typeof component.name !== 'string' || component.name.trim().length === 0) {
            errors.push('name is required and must be a non-empty string');
        }
        if (!component.type || typeof component.type !== 'string' || component.type.trim().length === 0) {
            errors.push('type is required and must be a non-empty string');
        }
        if (!component.properties || typeof component.properties !== 'object') {
            errors.push('properties must be an object');
        }
        if (!component.position || typeof component.position !== 'object') {
            errors.push('position must be an object');
        }
        else {
            if (typeof component.position.x !== 'number') {
                errors.push('position.x must be a number');
            }
            if (typeof component.position.y !== 'number') {
                errors.push('position.y must be a number');
            }
        }
        if (!component.connections || !Array.isArray(component.connections)) {
            errors.push('connections must be an array');
        }
        else {
            component.connections.forEach((connectionId, index) => {
                if (typeof connectionId !== 'string' || connectionId.trim().length === 0) {
                    errors.push(`connections[${index}] must be a non-empty string`);
                }
            });
        }
        return errors;
    }
    static validateConnection(connection) {
        const errors = [];
        if (!connection.id || typeof connection.id !== 'string' || connection.id.trim().length === 0) {
            errors.push('id is required and must be a non-empty string');
        }
        if (!connection.fromComponent || typeof connection.fromComponent !== 'string' || connection.fromComponent.trim().length === 0) {
            errors.push('fromComponent is required and must be a non-empty string');
        }
        if (!connection.toComponent || typeof connection.toComponent !== 'string' || connection.toComponent.trim().length === 0) {
            errors.push('toComponent is required and must be a non-empty string');
        }
        if (!connection.signal || typeof connection.signal !== 'string' || connection.signal.trim().length === 0) {
            errors.push('signal is required and must be a non-empty string');
        }
        if (!connection.properties || typeof connection.properties !== 'object') {
            errors.push('properties must be an object');
        }
        return errors;
    }
    static validateLayer(layer) {
        const errors = [];
        if (!layer.id || typeof layer.id !== 'string' || layer.id.trim().length === 0) {
            errors.push('id is required and must be a non-empty string');
        }
        if (!layer.name || typeof layer.name !== 'string' || layer.name.trim().length === 0) {
            errors.push('name is required and must be a non-empty string');
        }
        if (typeof layer.visible !== 'boolean') {
            errors.push('visible must be a boolean');
        }
        if (!layer.color || typeof layer.color !== 'string' || layer.color.trim().length === 0) {
            errors.push('color is required and must be a non-empty string');
        }
        if (layer.color && !/^#[0-9A-Fa-f]{6}$/.test(layer.color)) {
            errors.push('color must be a valid hex color (e.g., "#FF0000")');
        }
        return errors;
    }
    static validateSheet(sheet) {
        const errors = [];
        if (!sheet.id || typeof sheet.id !== 'string' || sheet.id.trim().length === 0) {
            errors.push('id is required and must be a non-empty string');
        }
        if (!sheet.name || typeof sheet.name !== 'string' || sheet.name.trim().length === 0) {
            errors.push('name is required and must be a non-empty string');
        }
        if (!sheet.size || typeof sheet.size !== 'string' || sheet.size.trim().length === 0) {
            errors.push('size is required and must be a non-empty string');
        }
        const validSizes = ['A0', 'A1', 'A2', 'A3', 'A4', 'B0', 'B1', 'B2', 'B3', 'B4', 'CUSTOM'];
        if (sheet.size && !validSizes.includes(sheet.size.toUpperCase())) {
            errors.push(`size must be one of: ${validSizes.join(', ')}`);
        }
        if (!sheet.components || !Array.isArray(sheet.components)) {
            errors.push('components must be an array');
        }
        else {
            sheet.components.forEach((componentId, index) => {
                if (typeof componentId !== 'string' || componentId.trim().length === 0) {
                    errors.push(`components[${index}] must be a non-empty string`);
                }
            });
        }
        return errors;
    }
    static create(data) {
        const projectData = {
            components: data?.components || [],
            connections: data?.connections || [],
            properties: data?.properties || {},
            layers: data?.layers || [],
            sheets: data?.sheets || []
        };
        const validation = E3ProjectData.validate(projectData);
        if (!validation.isValid) {
            throw new Error(`Invalid E3ProjectData: ${validation.errors.join(', ')}`);
        }
        return new E3ProjectData(projectData);
    }
    addComponent(component) {
        const validation = E3ProjectData.validateComponent(component);
        if (validation.length > 0) {
            throw new Error(`Invalid component: ${validation.join(', ')}`);
        }
        if (this.components.some(c => c.id === component.id)) {
            throw new Error(`Component with ID "${component.id}" already exists`);
        }
        this.components.push(component);
    }
    removeComponent(componentId) {
        const referencedInConnections = this.connections.some(c => c.fromComponent === componentId || c.toComponent === componentId);
        if (referencedInConnections) {
            throw new Error(`Cannot remove component "${componentId}" as it is referenced in connections`);
        }
        const referencedInSheets = this.sheets.some(s => s.components.includes(componentId));
        if (referencedInSheets) {
            throw new Error(`Cannot remove component "${componentId}" as it is referenced in sheets`);
        }
        this.components = this.components.filter(c => c.id !== componentId);
    }
    addConnection(connection) {
        const validation = E3ProjectData.validateConnection(connection);
        if (validation.length > 0) {
            throw new Error(`Invalid connection: ${validation.join(', ')}`);
        }
        if (this.connections.some(c => c.id === connection.id)) {
            throw new Error(`Connection with ID "${connection.id}" already exists`);
        }
        const componentIds = new Set(this.components.map(c => c.id));
        if (!componentIds.has(connection.fromComponent)) {
            throw new Error(`fromComponent "${connection.fromComponent}" does not exist`);
        }
        if (!componentIds.has(connection.toComponent)) {
            throw new Error(`toComponent "${connection.toComponent}" does not exist`);
        }
        this.connections.push(connection);
    }
    removeConnection(connectionId) {
        this.connections = this.connections.filter(c => c.id !== connectionId);
    }
    getComponentsOnSheet(sheetId) {
        const sheet = this.sheets.find(s => s.id === sheetId);
        if (!sheet) {
            throw new Error(`Sheet with ID "${sheetId}" not found`);
        }
        return this.components.filter(c => sheet.components.includes(c.id));
    }
    getConnectionsOnSheet(sheetId) {
        const sheet = this.sheets.find(s => s.id === sheetId);
        if (!sheet) {
            throw new Error(`Sheet with ID "${sheetId}" not found`);
        }
        const sheetComponentIds = new Set(sheet.components);
        return this.connections.filter(c => sheetComponentIds.has(c.fromComponent) && sheetComponentIds.has(c.toComponent));
    }
}
exports.E3ProjectData = E3ProjectData;
//# sourceMappingURL=E3ProjectData.js.map