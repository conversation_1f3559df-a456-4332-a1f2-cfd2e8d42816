{"version": 3, "file": "ProjectMetadataService.js", "sourceRoot": "", "sources": ["../../src/services/ProjectMetadataService.ts"], "names": [], "mappings": ";;;AACA,yEAAoE;AAsCpE,MAAa,sBAAsB;IAKjC,YAAY,QAAc,EAAE,oBAA0C;QACpE,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC1C,CAAC;IAKO,gCAAgC;QACtC,MAAM,YAAY,GAA6B;YAC7C;gBACE,KAAK,EAAE,aAAa;gBACpB,IAAI,EAAE,QAAQ;gBACd,aAAa,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC;aACjE;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE,QAAQ;gBACd,aAAa,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;aACrD;YACD;gBACE,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,CAAC;aACP;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE,MAAM;aACb;YACD;gBACE,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,OAAO;aACd;YACD;gBACE,KAAK,EAAE,cAAc;gBACrB,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,oBAAoB;aAC9B;YACD;gBACE,KAAK,EAAE,YAAY;gBACnB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,EAAE;aACd;YACD;gBACE,KAAK,EAAE,gBAAgB;gBACvB,IAAI,EAAE,SAAS;aAChB;SACF,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACpD,CAAC;IAKD,kBAAkB,CAAC,WAAmB,EAAE,KAA+B;QACrE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAKD,gBAAgB,CACd,QAA6B,EAC7B,cAAsB,SAAS;QAE/B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEjG,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAGnC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,CAAC;gBAC7E,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,eAAe,CAAC,CAAC;gBACjD,SAAS;YACX,CAAC;YAGD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1C,SAAS;YACX,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,qBAAqB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClE,SAAS;YACX,CAAC;YAGD,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACxD,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBACpD,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,sBAAsB,IAAI,CAAC,SAAS,kBAAkB,CAAC,CAAC;gBAC1F,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBACpD,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,0BAA0B,IAAI,CAAC,SAAS,kBAAkB,CAAC,CAAC;gBAC9F,CAAC;gBACD,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9C,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,mCAAmC,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;YAGD,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACxD,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,sBAAsB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBACpE,CAAC;gBACD,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,0BAA0B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;YAGD,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,qBAAqB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxF,CAAC;YAGD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC,KAAK,4BAA4B,CAAC,CAAC;gBACtF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAKO,YAAY,CAAC,KAAU,EAAE,YAAoB;QACnD,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YACnC,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpD,KAAK,SAAS;gBACZ,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;YACpC,KAAK,MAAM;gBACT,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3F,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ;gBACX,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9E;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAClB,SAAiB,EACjB,eAAoC,EACpC,SAAiB,EACjB,MAAe;QAEf,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,SAAS,EAAU,EACzB,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,qDAAqD,EACrD,mBAAmB,EACnB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACvC,6CAA6C,EAC7C,CAAC,SAAS,CAAC,CACZ,CAAC;YAEF,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,iDAAsB,CAC9B,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC;YAG7D,MAAM,WAAW,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,eAAe,EAAE,CAAC;YAG/D,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,iDAAsB,CAC9B,+BAA+B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAC7D,kBAAkB,EAClB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAG5B,MAAM,MAAM,CAAC,KAAK,CAChB,kEAAkE,EAClE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,SAAS,CAAC,CACrD,CAAC;gBAGF,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;oBAChE,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;oBAExC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC1D,MAAM,MAAM,CAAC,KAAK,CAChB;;uDAEyC,EACzC;4BACE,MAAM,CAAC,UAAU,EAAE;4BACnB,SAAS;4BACT,KAAK;4BACL,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;4BACxB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;4BACxB,SAAS;4BACT,IAAI,IAAI,EAAE;4BACV,MAAM,IAAI,IAAI;yBACf,CACF,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAE/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/B,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACxF,cAAc,EACd,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,MAAc,EACd,QAAgB,EAAE;QAElB,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,MAAM,EAAU,EACtB,SAAS,EACT,MAAM,EACN,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,mDAAmD,EACnD,mBAAmB,EACnB,SAAS,EACT,MAAM,CACP,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC;;;;kBAIU,EACV,CAAC,SAAS,EAAE,KAAK,CAAC,CACnB,CAAC;YAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;gBACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;gBACnC,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,CAAC,CAAC,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC7F,eAAe,EACf,SAAS,EACT,MAAM,CACP,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,SAAiB,EACjB,KAAa,EACb,QAAgB,EAChB,UAAkB,EAClB,MAAe;QAEf,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,UAAU,EAAU,EAC1B,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,qDAAqD,EACrD,mBAAmB,EACnB,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACtC,0EAA0E,EAC1E,CAAC,QAAQ,EAAE,SAAS,CAAC,CACtB,CAAC;YAEF,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,iDAAsB,CAC9B,yBAAyB,EACzB,WAAW,EACX,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAG9C,MAAM,IAAI,CAAC,cAAc,CACvB,SAAS,EACT,EAAE,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,EACrB,UAAU,EACV,MAAM,IAAI,mBAAmB,QAAQ,EAAE,CACxC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC9F,cAAc,EACd,SAAS,EACT,UAAU,CACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAxZD,wDAwZC"}