{"version": 3, "file": "ProjectHierarchyService.js", "sourceRoot": "", "sources": ["../../src/services/ProjectHierarchyService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAA6B;AAC7B,uCAAyB;AACzB,+BAAiC;AACjC,yEAAoE;AAIpE,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,KAAK,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAClC,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAqBpC,MAAa,uBAAuB;IAKlC,YACE,QAAc,EACd,oBAA0C,EAC1C,cAAsB,oBAAoB;QAE1C,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;IACrC,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,SAAiB;QAC/D,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,SAAS,EAAU,EACzB,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,sDAAsD,EACtD,mBAAmB,EACnB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAG/D,MAAM,KAAK,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAG9C,MAAM,eAAe,GAAG;gBACtB,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,0CAA0C,EAAE;gBAC9E,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,2BAA2B,EAAE;gBAChE,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,4BAA4B,EAAE;gBAC/D,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,+BAA+B,EAAE;gBACjE,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,+BAA+B,EAAE;aACnE,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAE5B,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;oBACrC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;oBACvD,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAG7C,MAAM,MAAM,CAAC,KAAK,CAChB;qDACyC,EACzC;wBACE,MAAM,CAAC,UAAU,EAAE;wBACnB,SAAS;wBACT,MAAM,CAAC,IAAI;wBACX,UAAU;wBACV,SAAS;wBACT,IAAI,IAAI,EAAE;wBACV,IAAI,IAAI,EAAE;wBACV,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;qBACtE,CACF,CAAC;gBACJ,CAAC;gBAED,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAE/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAE/B,IAAI,CAAC;oBACH,MAAM,KAAK,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAChD,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE,YAAY,CAAC,CAAC;gBACrE,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,uCAAuC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACjG,iBAAiB,EACjB,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAChB,SAAiB,EACjB,UAAkB,EAClB,QAA4B,EAC5B,SAAiB,EACjB,WAAgC,EAAE;QAElC,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,SAAS,EAAU,EACzB,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,2CAA2C,EAC3C,mBAAmB,EACnB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,iDAAsB,CAC9B,6BAA6B,EAC7B,kBAAkB,EAClB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;YAGjE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAE5D,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACtC,oEAAoE,EACpE,CAAC,QAAQ,EAAE,SAAS,CAAC,CACtB,CAAC;gBAEF,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,MAAM,IAAI,iDAAsB,CAC9B,yBAAyB,EACzB,WAAW,EACX,SAAS,EACT,SAAS,CACV,CAAC;gBACJ,CAAC;gBAED,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACzC,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAGxD,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzB,MAAM,IAAI,iDAAsB,CAC9B,uBAAuB,EACvB,gBAAgB,EAChB,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAGD,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAG7C,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACjB;qDAC6C,EAC7C;gBACE,QAAQ;gBACR,SAAS;gBACT,aAAa;gBACb,QAAQ,IAAI,IAAI;gBAChB,UAAU;gBACV,SAAS;gBACT,GAAG;gBACH,GAAG;gBACH,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;aACzB,CACF,CAAC;YAEF,MAAM,MAAM,GAAkB;gBAC5B,EAAE,EAAE,QAAQ;gBACZ,SAAS;gBACT,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,UAAU;gBAChB,SAAS;gBACT,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG;gBACd,QAAQ;aACT,CAAC;YAEF,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC7B,CAAC;YAED,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACtF,cAAc,EACd,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,MAAc;QACzD,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,MAAM,EAAU,EACtB,SAAS,EACT,MAAM,EACN,SAAS,CACV,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,oDAAoD,EACpD,mBAAmB,EACnB,SAAS,EACT,MAAM,CACP,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC;;;8CAGsC,EACtC,CAAC,SAAS,CAAC,CACZ,CAAC;YAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7B,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,QAAQ,EAAE,GAAG,CAAC,SAAS;gBACvB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,SAAS,EAAE,GAAG,CAAC,UAAU;gBACzB,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;aAC7B,CAAC,CAAC,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EAC9F,iBAAiB,EACjB,SAAS,EACT,MAAM,CACP,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,SAAiB;QACpD,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACtC,6CAA6C,EAC7C,CAAC,QAAQ,CAAC,CACX,CAAC;YAEF,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,iDAAsB,CAC9B,kBAAkB,EAClB,WAAW,EACX,SAAS,EACT,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAGpC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,EAAE,EAAE,EAAE,SAAS,EAAU,EACzB,SAAS,EACT,OAAO,EACP,MAAM,CAAC,UAAU,CAClB,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,iDAAsB,CAC9B,2CAA2C,EAC3C,mBAAmB,EACnB,MAAM,CAAC,UAAU,EACjB,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,MAAM,QAAQ,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;gBAClD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAC7B,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;YAC5B,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,MAAM,IAAI,iDAAsB,CAC9B,wCAAwC,EACxC,mBAAmB,EACnB,MAAM,CAAC,UAAU,EACjB,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACxC,oEAAoE,EACpE,CAAC,QAAQ,CAAC,CACX,CAAC;YAEF,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,iDAAsB,CAC9B,sCAAsC,EACtC,mBAAmB,EACnB,MAAM,CAAC,UAAU,EACjB,SAAS,CACV,CAAC;YACJ,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACrC,iFAAiF,EACjF,CAAC,QAAQ,CAAC,CACX,CAAC;YAEF,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,iDAAsB,CAC9B,uCAAuC,EACvC,mBAAmB,EACnB,MAAM,CAAC,UAAU,EACjB,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAG5B,MAAM,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAG5E,IAAI,CAAC;oBACH,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3B,CAAC;gBAAC,OAAO,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,IAAI,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;gBAE7D,CAAC;gBAED,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAE/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/B,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iDAAsB,EAAE,CAAC;gBAC5C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,iDAAsB,CAC9B,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,EACtF,cAAc,EACd,SAAS,EACT,SAAS,CACV,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA/ZD,0DA+ZC"}