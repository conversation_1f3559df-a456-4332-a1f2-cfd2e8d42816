import { Pool } from 'pg';
import { AuthorizationService } from './AuthorizationService';
export interface ArchiveRecord {
    id: string;
    projectId: string;
    archiveType: 'manual' | 'automatic' | 'scheduled';
    archivePath: string;
    archivedBy: string;
    archivedAt: Date;
    reason?: string;
    metadata: Record<string, any>;
}
export interface RestoreOptions {
    restoreFiles: boolean;
    restoreStructure: boolean;
    restorePermissions: boolean;
    targetProjectId?: string;
}
export declare class ProjectArchivalService {
    private db;
    private authService;
    private archivePath;
    constructor(database: Pool, authorizationService: AuthorizationService, archiveStoragePath?: string);
    createProjectArchive(projectId: string, archivedBy: string, archiveType?: 'manual' | 'automatic' | 'scheduled', reason?: string): Promise<ArchiveRecord>;
    private archiveProjectStructure;
    private archiveProjectFiles;
    private archiveProjectPermissions;
    private getProjectFileCount;
    private getProjectTotalSize;
    restoreProjectFromArchive(archiveId: string, restoredBy: string, options?: RestoreOptions): Promise<string>;
    private restoreProjectStructure;
    private restoreProjectFiles;
    private restoreProjectPermissions;
}
//# sourceMappingURL=ProjectArchivalService.d.ts.map