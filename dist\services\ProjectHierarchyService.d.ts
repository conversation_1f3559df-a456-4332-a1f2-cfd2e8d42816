import { Pool } from 'pg';
import { AuthorizationService } from './AuthorizationService';
export interface ProjectFolder {
    id: string;
    projectId: string;
    name: string;
    parentId?: string;
    path: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
    metadata: Record<string, any>;
}
export declare class ProjectHierarchyService {
    private db;
    private authService;
    private baseStoragePath;
    constructor(database: Pool, authorizationService: AuthorizationService, storagePath?: string);
    createProjectStructure(projectId: string, createdBy: string): Promise<void>;
    createFolder(projectId: string, folderName: string, parentId: string | undefined, createdBy: string, metadata?: Record<string, any>): Promise<ProjectFolder>;
    getProjectStructure(projectId: string, userId: string): Promise<ProjectFolder[]>;
    deleteFolder(folderId: string, deletedBy: string): Promise<void>;
}
//# sourceMappingURL=ProjectHierarchyService.d.ts.map