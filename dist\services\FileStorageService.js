"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileStorageService = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
const util_1 = require("util");
const mkdir = (0, util_1.promisify)(fs.mkdir);
const writeFile = (0, util_1.promisify)(fs.writeFile);
const readFile = (0, util_1.promisify)(fs.readFile);
const unlink = (0, util_1.promisify)(fs.unlink);
const stat = (0, util_1.promisify)(fs.stat);
const access = (0, util_1.promisify)(fs.access);
class FileStorageService {
    constructor(config) {
        this.config = {
            baseStoragePath: config?.baseStoragePath || path.join(process.cwd(), 'storage'),
            maxFileSize: config?.maxFileSize || 1024 * 1024 * 1024,
            allowedMimeTypes: config?.allowedMimeTypes || {
                'e3series': ['application/octet-stream', 'application/x-e3series'],
                'pdf': ['application/pdf'],
                'dxf': ['application/dxf', 'image/vnd.dxf', 'application/octet-stream']
            }
        };
    }
    async initialize() {
        try {
            await access(this.config.baseStoragePath);
        }
        catch {
            await mkdir(this.config.baseStoragePath, { recursive: true });
        }
    }
    async storeFile(projectId, fileData, fileId, version = '1.0.0') {
        const projectDir = path.join(this.config.baseStoragePath, projectId);
        try {
            await access(projectDir);
        }
        catch {
            await mkdir(projectDir, { recursive: true });
        }
        const versionDir = path.join(projectDir, fileId);
        try {
            await access(versionDir);
        }
        catch {
            await mkdir(versionDir, { recursive: true });
        }
        const fileExtension = path.extname(fileData.originalName);
        const fileName = `${version}${fileExtension}`;
        const filePath = path.join(versionDir, fileName);
        await writeFile(filePath, fileData.buffer);
        return filePath;
    }
    getFileStream(filePath) {
        return fs.createReadStream(filePath);
    }
    async retrieveFile(filePath) {
        try {
            await access(filePath);
            return await readFile(filePath);
        }
        catch (error) {
            throw new Error(`File not found or inaccessible: ${filePath}`);
        }
    }
    async deleteFile(filePath) {
        try {
            await access(filePath);
            await unlink(filePath);
        }
        catch (error) {
            throw new Error(`Failed to delete file: ${filePath}`);
        }
    }
    async fileExists(filePath) {
        try {
            await access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    async getFileStats(filePath) {
        try {
            return await stat(filePath);
        }
        catch (error) {
            throw new Error(`Failed to get file stats: ${filePath}`);
        }
    }
    calculateChecksum(buffer) {
        return crypto.createHash('sha256').update(buffer).digest('hex');
    }
    async calculateChecksumFromFile(filePath) {
        const buffer = await this.retrieveFile(filePath);
        return this.calculateChecksum(buffer);
    }
    validateFileFormat(fileData, expectedType) {
        const errors = [];
        const warnings = [];
        if (fileData.size > this.config.maxFileSize) {
            errors.push(`File size (${fileData.size} bytes) exceeds maximum allowed size (${this.config.maxFileSize} bytes)`);
        }
        if (fileData.size === 0) {
            errors.push('File is empty');
        }
        const allowedMimeTypes = this.config.allowedMimeTypes[expectedType];
        if (!allowedMimeTypes.includes(fileData.mimetype)) {
            warnings.push(`MIME type '${fileData.mimetype}' may not be compatible with file type '${expectedType}'`);
        }
        const fileExtension = path.extname(fileData.originalName).toLowerCase();
        const expectedExtensions = this.getExpectedExtensions(expectedType);
        if (!expectedExtensions.includes(fileExtension)) {
            errors.push(`File extension '${fileExtension}' is not valid for file type '${expectedType}'. Expected: ${expectedExtensions.join(', ')}`);
        }
        const contentValidation = this.validateFileContent(fileData.buffer, expectedType);
        errors.push(...contentValidation.errors);
        warnings.push(...contentValidation.warnings);
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    getExpectedExtensions(fileType) {
        const extensions = {
            'e3series': ['.e3s', '.e3p', '.e3d'],
            'pdf': ['.pdf'],
            'dxf': ['.dxf']
        };
        return extensions[fileType];
    }
    validateFileContent(buffer, fileType) {
        const errors = [];
        const warnings = [];
        if (buffer.length === 0) {
            errors.push('File content is empty');
            return { errors, warnings };
        }
        switch (fileType) {
            case 'pdf':
                if (!buffer.subarray(0, 4).toString().startsWith('%PDF')) {
                    errors.push('File does not appear to be a valid PDF (missing PDF header)');
                }
                break;
            case 'dxf':
                const dxfHeader = buffer.subarray(0, 100).toString('utf8');
                if (!dxfHeader.includes('SECTION') || !dxfHeader.includes('HEADER')) {
                    errors.push('File does not appear to be a valid DXF (missing DXF structure)');
                }
                break;
            case 'e3series':
                if (buffer.length < 100) {
                    warnings.push('E3 series file appears to be very small, please verify it is complete');
                }
                break;
        }
        return { errors, warnings };
    }
    async verifyFileIntegrity(filePath, expectedChecksum) {
        try {
            const buffer = await this.retrieveFile(filePath);
            const actualChecksum = this.calculateChecksum(buffer);
            return actualChecksum === expectedChecksum;
        }
        catch {
            return false;
        }
    }
    getProjectStoragePath(projectId) {
        return path.join(this.config.baseStoragePath, projectId);
    }
    getFileStoragePath(projectId, fileId) {
        return path.join(this.config.baseStoragePath, projectId, fileId);
    }
    async cleanupEmptyDirectories(dirPath) {
        try {
            const files = await (0, util_1.promisify)(fs.readdir)(dirPath);
            if (files.length === 0) {
                await (0, util_1.promisify)(fs.rmdir)(dirPath);
                const parentDir = path.dirname(dirPath);
                if (parentDir !== this.config.baseStoragePath) {
                    await this.cleanupEmptyDirectories(parentDir);
                }
            }
        }
        catch {
        }
    }
}
exports.FileStorageService = FileStorageService;
//# sourceMappingURL=FileStorageService.js.map