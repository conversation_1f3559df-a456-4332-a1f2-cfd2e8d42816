import { Pool } from 'pg';
import { FileManagementService } from '../FileManagementService';
import { FileStorageService } from '../FileStorageService';
import { FileUploadService } from '../FileUploadService';
import { VersionControlService } from '../VersionControlService';
import { FileParserService } from '../FileParserService';
import { FileMetadata } from '../../types';

// Mock dependencies
const mockDb = {
  connect: jest.fn(),
  query: jest.fn(),
} as unknown as Pool;

const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

const mockFileStorageService = {
  getFileStream: jest.fn(),
  calculateChecksum: jest.fn(),
  calculateChecksumFromFile: jest.fn(),
  storeFile: jest.fn(),
} as unknown as FileStorageService;

const mockFileUploadService = {
  processUpload: jest.fn(),
  getFileTypeFromExtension: jest.fn(),
  validateFileBeforeUpload: jest.fn(),
} as unknown as FileUploadService;

const mockVersionControlService = {
  createVersion: jest.fn(),
  getVersionHistory: jest.fn(),
  getVersion: jest.fn(),
  getLatestVersion: jest.fn(),
  getReleasedVersions: jest.fn(),
  getVersionStatistics: jest.fn(),
} as unknown as VersionControlService;

const mockFileParserService = {
  parsePDFMetadata: jest.fn(),
  parseDXFMetadata: jest.fn(),
  parseE3File: jest.fn(),
} as unknown as FileParserService;

describe('FileManagementService', () => {
  let service: FileManagementService;

  beforeEach(() => {
    service = new FileManagementService(
      mockFileStorageService,
      mockFileUploadService,
      mockVersionControlService,
      mockFileParserService,
      mockDb
    );
    jest.clearAllMocks();
    (mockDb.connect as jest.Mock).mockResolvedValue(mockClient);
  });

  describe('uploadFile', () => {
    const mockFile = {
      buffer: Buffer.from('test file content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024,
    } as Express.Multer.File;

    it('should upload a file successfully', async () => {
      const projectId = 'project-123';
      const uploadedBy = 'user-123';
      const fileType = 'pdf';
      const metadata: FileMetadata = { title: 'Test File' };

      // Mock database responses
      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: projectId }] }) // Project exists check
        .mockResolvedValueOnce({ rows: [] }) // Insert file
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      // Mock service responses
      (mockFileUploadService.getFileTypeFromExtension as jest.Mock).mockReturnValue('pdf');
      (mockFileParserService.parsePDFMetadata as jest.Mock).mockResolvedValue({
        title: 'Test PDF',
        pageCount: 1
      });
      (mockFileUploadService.processUpload as jest.Mock).mockResolvedValue({
        id: 'file-123',
        name: 'test.pdf',
        originalName: 'test.pdf',
        size: 1024,
        checksum: 'abc123',
        projectId,
        uploadedBy,
        uploadedAt: new Date(),
        currentVersion: '1.0.0',
        fileType: 'pdf',
        metadata: {},
        versions: []
      });

      (mockVersionControlService.createVersion as jest.Mock).mockResolvedValue({
        id: 'version-123',
        version: '1.0.0',
        fileId: 'file-123',
        createdBy: uploadedBy,
        createdAt: new Date(),
        changes: 'Initial upload',
        isLocked: false,
        isReleased: false,
        filePath: 'files/file-123/1.0.0/test.pdf'
      });

      const result = await service.uploadFile(mockFile, projectId, uploadedBy, fileType, metadata);

      expect(result).toBeDefined();
      expect(result.projectId).toBe(projectId);
      expect(result.uploadedBy).toBe(uploadedBy);
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
    });

    it('should rollback transaction on error', async () => {
      const projectId = 'project-123';
      const uploadedBy = 'user-123';
      const fileType = 'pdf';

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockRejectedValueOnce(new Error('Database error')); // Project check fails

      await expect(service.uploadFile(mockFile, projectId, uploadedBy, fileType))
        .rejects.toThrow('Database error');
      
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });

    it('should throw error if project not found', async () => {
      const projectId = 'nonexistent-project';
      const uploadedBy = 'user-123';
      const fileType = 'pdf';

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [] }); // Project not found

      await expect(service.uploadFile(mockFile, projectId, uploadedBy, fileType))
        .rejects.toThrow(`Project ${projectId} not found or not active`);
    });
  });

  describe('downloadFile', () => {
    it('should download a file successfully', async () => {
      const fileId = 'file-123';
      const mockStream = { pipe: jest.fn() } as any;

      (mockDb.query as jest.Mock).mockResolvedValue({
        rows: [{ id: fileId, name: 'test.pdf', is_deleted: false }]
      });

      (mockVersionControlService.getLatestVersion as jest.Mock).mockResolvedValue({
        id: 'version-123',
        filePath: 'files/file-123/1.0.0/test.pdf'
      });

      (mockFileStorageService.getFileStream as jest.Mock).mockReturnValue(mockStream);

      const result = await service.downloadFile(fileId);

      expect(result).toBe(mockStream);
      expect(mockFileStorageService.getFileStream).toHaveBeenCalledWith('files/file-123/1.0.0/test.pdf');
    });

    it('should throw error if file not found', async () => {
      const fileId = 'nonexistent-file';

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: [] });

      await expect(service.downloadFile(fileId))
        .rejects.toThrow(`File ${fileId} not found`);
    });
  });

  describe('deleteFile', () => {
    it('should soft delete a file successfully', async () => {
      const fileId = 'file-123';

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: fileId }] }) // File exists check
        .mockResolvedValueOnce({ rows: [] }) // Update file
        .mockResolvedValueOnce({ rows: [] }); // COMMIT

      (mockVersionControlService.getReleasedVersions as jest.Mock).mockResolvedValue([]);

      await expect(service.deleteFile(fileId)).resolves.not.toThrow();
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
    });

    it('should prevent deletion of files with released versions', async () => {
      const fileId = 'file-123';

      mockClient.query
        .mockResolvedValueOnce({ rows: [] }) // BEGIN
        .mockResolvedValueOnce({ rows: [{ id: fileId }] }); // File exists check

      (mockVersionControlService.getReleasedVersions as jest.Mock).mockResolvedValue([
        { id: 'version-123', isReleased: true }
      ]);

      await expect(service.deleteFile(fileId))
        .rejects.toThrow(`Cannot delete file ${fileId} - it has released versions`);
      
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
    });
  });

  describe('listFiles', () => {
    it('should list files in a project', async () => {
      const projectId = 'project-123';
      const mockFiles = [
        {
          id: 'file-1',
          project_id: projectId,
          name: 'test1.pdf',
          original_name: 'test1.pdf',
          file_type: 'pdf',
          size: '1024',
          checksum: 'abc123',
          uploaded_by: 'user-123',
          uploaded_at: new Date(),
          current_version: '1.0.0',
          metadata: {}
        }
      ];

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: mockFiles });
      (mockVersionControlService.getVersionHistory as jest.Mock).mockResolvedValue([]);

      const result = await service.listFiles(projectId);

      expect(result).toHaveLength(1);
      expect(result[0]!.id).toBe('file-1');
      expect(result[0]!.projectId).toBe(projectId);
    });

    it('should apply filters when listing files', async () => {
      const projectId = 'project-123';
      const filters = { fileType: 'pdf' as const, author: 'john' };

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: [] });

      await service.listFiles(projectId, filters);

      const queryCall = (mockDb.query as jest.Mock).mock.calls[0];
      expect(queryCall[0]).toContain('f.file_type = $2');
      expect(queryCall[0]).toContain('u.username ILIKE $3');
      expect(queryCall[1]).toEqual([projectId, 'pdf', '%john%']);
    });
  });

  describe('extractMetadata', () => {
    const mockFile = {
      buffer: Buffer.from('test content'),
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      size: 1024,
    } as Express.Multer.File;

    it('should extract PDF metadata', async () => {
      (mockFileUploadService.getFileTypeFromExtension as jest.Mock).mockReturnValue('pdf');
      (mockFileParserService.parsePDFMetadata as jest.Mock).mockResolvedValue({
        title: 'Test PDF',
        author: 'Test Author',
        pageCount: 5
      });

      const result = await service.extractMetadata(mockFile);

      expect(result.title).toBe('Test PDF');
      expect(result.author).toBe('Test Author');
      expect(result.pageCount).toBe(5);
    });

    it('should handle metadata extraction errors gracefully', async () => {
      (mockFileUploadService.getFileTypeFromExtension as jest.Mock).mockReturnValue('pdf');
      (mockFileParserService.parsePDFMetadata as jest.Mock).mockRejectedValue(new Error('Parse error'));

      const result = await service.extractMetadata(mockFile);

      expect(result.title).toBe('test.pdf');
      expect(result.pageCount).toBe(1); // Fallback value
    });
  });

  describe('fileExists', () => {
    it('should return true if file exists', async () => {
      const fileId = 'file-123';

      (mockDb.query as jest.Mock).mockResolvedValue({
        rows: [{ id: fileId }]
      });

      const result = await service.fileExists(fileId);

      expect(result).toBe(true);
    });

    it('should return false if file does not exist', async () => {
      const fileId = 'nonexistent-file';

      (mockDb.query as jest.Mock).mockResolvedValue({ rows: [] });

      const result = await service.fileExists(fileId);

      expect(result).toBe(false);
    });
  });

  describe('validateFile', () => {
    it('should validate file successfully', async () => {
      const mockFile = {
        originalname: 'test.pdf',
        size: 1024,
      } as Express.Multer.File;

      (mockFileUploadService.validateFileBeforeUpload as jest.Mock).mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: []
      });

      const result = await service.validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should add warnings for file name issues', async () => {
      const mockFile = {
        originalname: 'test file with spaces.pdf',
        size: 1024,
      } as Express.Multer.File;

      (mockFileUploadService.validateFileBeforeUpload as jest.Mock).mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: []
      });

      const result = await service.validateFile(mockFile);

      expect(result.warnings).toContain('File name contains spaces, which may cause issues in some systems');
    });
  });
});
