import { Pool, PoolClient, QueryResult, QueryResultRow } from 'pg';
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: boolean;
    max?: number;
    idleTimeoutMillis?: number;
    connectionTimeoutMillis?: number;
}
export declare class DatabaseConnection {
    private pool;
    private static instance;
    private constructor();
    static getInstance(config?: DatabaseConfig): DatabaseConnection;
    static getDefaultConfig(): DatabaseConfig;
    query<T extends QueryResultRow = any>(text: string, params?: any[]): Promise<QueryResult<T>>;
    getPool(): Pool;
    getClient(): Promise<PoolClient>;
    transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T>;
    initializeSchema(): Promise<void>;
    testConnection(): Promise<boolean>;
    getPoolStats(): {
        totalCount: number;
        idleCount: number;
        waitingCount: number;
    };
    close(): Promise<void>;
}
export declare const getDatabase: (config?: DatabaseConfig) => DatabaseConnection;
export declare const dbConfig: DatabaseConfig;
//# sourceMappingURL=connection.d.ts.map