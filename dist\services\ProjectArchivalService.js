"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectArchivalService = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const util_1 = require("util");
const ProjectManagementService_1 = require("./ProjectManagementService");
const access = (0, util_1.promisify)(fs.access);
const mkdir = (0, util_1.promisify)(fs.mkdir);
const copyFile = (0, util_1.promisify)(fs.copyFile);
const readdir = (0, util_1.promisify)(fs.readdir);
const stat = (0, util_1.promisify)(fs.stat);
const rmdir = (0, util_1.promisify)(fs.rmdir);
class ProjectArchivalService {
    constructor(database, authorizationService, archiveStoragePath = './storage/archives') {
        this.db = database;
        this.authService = authorizationService;
        this.archivePath = archiveStoragePath;
    }
    async createProjectArchive(projectId, archivedBy, archiveType = 'manual', reason) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: archivedBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to archive project', 'PERMISSION_DENIED', projectId, archivedBy);
            }
            const projectResult = await this.db.query('SELECT * FROM projects WHERE id = $1', [projectId]);
            if (projectResult.rows.length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Project not found', 'NOT_FOUND', projectId, archivedBy);
            }
            const project = projectResult.rows[0];
            const archiveId = crypto.randomUUID();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const archiveDir = path.join(this.archivePath, `${project.name}_${timestamp}_${archiveId}`);
            await mkdir(archiveDir, { recursive: true });
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                const metadataPath = path.join(archiveDir, 'project_metadata.json');
                const projectMetadata = {
                    project,
                    archivedAt: new Date(),
                    archivedBy,
                    reason,
                    archiveType
                };
                await fs.promises.writeFile(metadataPath, JSON.stringify(projectMetadata, null, 2));
                await this.archiveProjectStructure(projectId, archiveDir);
                await this.archiveProjectFiles(projectId, archiveDir);
                await this.archiveProjectPermissions(projectId, archiveDir);
                const archiveRecord = {
                    id: archiveId,
                    projectId,
                    archiveType,
                    archivePath: archiveDir,
                    archivedBy,
                    archivedAt: new Date(),
                    reason,
                    metadata: {
                        projectName: project.name,
                        originalCreatedBy: project.created_by,
                        originalCreatedAt: project.created_at,
                        fileCount: await this.getProjectFileCount(projectId),
                        totalSize: await this.getProjectTotalSize(projectId)
                    }
                };
                await client.query(`INSERT INTO project_archives 
           (id, project_id, archive_type, archive_path, archived_by, archived_at, reason, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`, [
                    archiveRecord.id,
                    archiveRecord.projectId,
                    archiveRecord.archiveType,
                    archiveRecord.archivePath,
                    archiveRecord.archivedBy,
                    archiveRecord.archivedAt,
                    archiveRecord.reason,
                    JSON.stringify(archiveRecord.metadata)
                ]);
                await client.query('UPDATE projects SET status = $1, updated_at = $2 WHERE id = $3', ['archived', new Date(), projectId]);
                await client.query('COMMIT');
                return archiveRecord;
            }
            catch (error) {
                await client.query('ROLLBACK');
                try {
                    await rmdir(archiveDir, { recursive: true });
                }
                catch (cleanupError) {
                    console.warn('Failed to cleanup archive directory:', cleanupError);
                }
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to create project archive: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ARCHIVE_ERROR', projectId, archivedBy);
        }
    }
    async archiveProjectStructure(projectId, archiveDir) {
        const structureResult = await this.db.query('SELECT * FROM project_folders WHERE project_id = $1 ORDER BY path', [projectId]);
        const structurePath = path.join(archiveDir, 'project_structure.json');
        await fs.promises.writeFile(structurePath, JSON.stringify(structureResult.rows, null, 2));
    }
    async archiveProjectFiles(projectId, archiveDir) {
        const filesResult = await this.db.query('SELECT * FROM files WHERE project_id = $1 AND is_deleted = FALSE', [projectId]);
        const filesMetadataPath = path.join(archiveDir, 'files_metadata.json');
        await fs.promises.writeFile(filesMetadataPath, JSON.stringify(filesResult.rows, null, 2));
        const filesArchiveDir = path.join(archiveDir, 'files');
        await mkdir(filesArchiveDir, { recursive: true });
        for (const file of filesResult.rows) {
            try {
                const sourcePath = path.join('./storage/files', file.id);
                const targetPath = path.join(filesArchiveDir, `${file.id}_${file.original_name}`);
                await access(sourcePath);
                await copyFile(sourcePath, targetPath);
            }
            catch (error) {
                console.warn(`Failed to archive file ${file.id}:`, error);
            }
        }
    }
    async archiveProjectPermissions(projectId, archiveDir) {
        const permissionsResult = await this.db.query('SELECT * FROM project_permissions WHERE project_id = $1', [projectId]);
        const permissionsPath = path.join(archiveDir, 'project_permissions.json');
        await fs.promises.writeFile(permissionsPath, JSON.stringify(permissionsResult.rows, null, 2));
    }
    async getProjectFileCount(projectId) {
        const result = await this.db.query('SELECT COUNT(*) as count FROM files WHERE project_id = $1 AND is_deleted = FALSE', [projectId]);
        return parseInt(result.rows[0].count);
    }
    async getProjectTotalSize(projectId) {
        const result = await this.db.query('SELECT COALESCE(SUM(size), 0) as total_size FROM files WHERE project_id = $1 AND is_deleted = FALSE', [projectId]);
        return parseInt(result.rows[0].total_size);
    }
    async restoreProjectFromArchive(archiveId, restoredBy, options = {
        restoreFiles: true,
        restoreStructure: true,
        restorePermissions: true
    }) {
        try {
            const archiveResult = await this.db.query('SELECT * FROM project_archives WHERE id = $1', [archiveId]);
            if (archiveResult.rows.length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Archive not found', 'NOT_FOUND', undefined, restoredBy);
            }
            const archive = archiveResult.rows[0];
            const hasPermission = await this.authService.hasPermission({ id: restoredBy }, 'system', 'admin') || archive.archived_by === restoredBy;
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to restore project', 'PERMISSION_DENIED', archive.project_id, restoredBy);
            }
            await access(archive.archive_path);
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                const projectId = options.targetProjectId || archive.project_id;
                const metadataPath = path.join(archive.archive_path, 'project_metadata.json');
                const projectMetadata = JSON.parse(await fs.promises.readFile(metadataPath, 'utf-8'));
                await client.query(`INSERT INTO projects (id, name, description, created_by, created_at, updated_at, status, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
           ON CONFLICT (id) DO UPDATE SET 
           status = 'active', updated_at = $6`, [
                    projectId,
                    projectMetadata.project.name + (options.targetProjectId ? '_restored' : ''),
                    projectMetadata.project.description,
                    projectMetadata.project.created_by,
                    projectMetadata.project.created_at,
                    new Date(),
                    'active',
                    JSON.stringify(projectMetadata.project.metadata)
                ]);
                if (options.restoreStructure) {
                    await this.restoreProjectStructure(archive.archive_path, projectId, client);
                }
                if (options.restoreFiles) {
                    await this.restoreProjectFiles(archive.archive_path, projectId, client);
                }
                if (options.restorePermissions) {
                    await this.restoreProjectPermissions(archive.archive_path, projectId, client);
                }
                await client.query('COMMIT');
                return projectId;
            }
            catch (error) {
                await client.query('ROLLBACK');
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to restore project: ${error instanceof Error ? error.message : 'Unknown error'}`, 'RESTORE_ERROR', undefined, restoredBy);
        }
    }
    async restoreProjectStructure(archivePath, projectId, client) {
        const structurePath = path.join(archivePath, 'project_structure.json');
        try {
            const structureData = JSON.parse(await fs.promises.readFile(structurePath, 'utf-8'));
            for (const folder of structureData) {
                await client.query(`INSERT INTO project_folders (id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
           ON CONFLICT (id) DO NOTHING`, [
                    folder.id,
                    projectId,
                    folder.name,
                    folder.parent_id,
                    folder.path.replace(folder.project_id, projectId),
                    folder.created_by,
                    folder.created_at,
                    folder.updated_at,
                    JSON.stringify(folder.metadata)
                ]);
            }
        }
        catch (error) {
            console.warn('Failed to restore project structure:', error);
        }
    }
    async restoreProjectFiles(archivePath, projectId, client) {
        const filesMetadataPath = path.join(archivePath, 'files_metadata.json');
        try {
            const filesData = JSON.parse(await fs.promises.readFile(filesMetadataPath, 'utf-8'));
            const filesArchiveDir = path.join(archivePath, 'files');
            for (const file of filesData) {
                await client.query(`INSERT INTO files (id, project_id, folder_id, name, original_name, file_type, size, checksum, uploaded_by, uploaded_at, current_version, metadata, is_deleted)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
           ON CONFLICT (id) DO NOTHING`, [
                    file.id,
                    projectId,
                    file.folder_id,
                    file.name,
                    file.original_name,
                    file.file_type,
                    file.size,
                    file.checksum,
                    file.uploaded_by,
                    file.uploaded_at,
                    file.current_version,
                    JSON.stringify(file.metadata),
                    false
                ]);
                try {
                    const sourcePath = path.join(filesArchiveDir, `${file.id}_${file.original_name}`);
                    const targetPath = path.join('./storage/files', file.id);
                    await access(sourcePath);
                    await copyFile(sourcePath, targetPath);
                }
                catch (error) {
                    console.warn(`Failed to restore file ${file.id}:`, error);
                }
            }
        }
        catch (error) {
            console.warn('Failed to restore project files:', error);
        }
    }
    async restoreProjectPermissions(archivePath, projectId, client) {
        const permissionsPath = path.join(archivePath, 'project_permissions.json');
        try {
            const permissionsData = JSON.parse(await fs.promises.readFile(permissionsPath, 'utf-8'));
            for (const permission of permissionsData) {
                await client.query(`INSERT INTO project_permissions (id, project_id, user_id, role, granted_by, granted_at)
           VALUES ($1, $2, $3, $4, $5, $6)
           ON CONFLICT (project_id, user_id) DO UPDATE SET 
           role = $4, granted_by = $5, granted_at = $6`, [
                    permission.id,
                    projectId,
                    permission.user_id,
                    permission.role,
                    permission.granted_by,
                    permission.granted_at
                ]);
            }
        }
        catch (error) {
            console.warn('Failed to restore project permissions:', error);
        }
    }
}
exports.ProjectArchivalService = ProjectArchivalService;
//# sourceMappingURL=ProjectArchivalService.js.map