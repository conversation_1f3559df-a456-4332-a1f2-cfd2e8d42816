import { Pool } from 'pg';
import { User } from '../types';
export interface Session {
    id: string;
    userId: string;
    token: string;
    refreshToken: string;
    expiresAt: Date;
    createdAt: Date;
    lastAccessedAt: Date;
    ipAddress?: string;
    userAgent?: string;
    isActive: boolean;
}
export interface SessionService {
    createSession(user: User, token: string, refreshToken: string, ipAddress?: string, userAgent?: string): Promise<Session>;
    getSession(sessionId: string): Promise<Session | null>;
    getSessionByToken(token: string): Promise<Session | null>;
    refreshSession(refreshToken: string): Promise<{
        session: Session;
        newToken: string;
    }>;
    invalidateSession(sessionId: string): Promise<void>;
    invalidateAllUserSessions(userId: string): Promise<void>;
    cleanupExpiredSessions(): Promise<number>;
    getUserActiveSessions(userId: string): Promise<Session[]>;
}
export declare class SessionServiceImpl implements SessionService {
    private db;
    private sessionExpiryHours;
    constructor(db: Pool, sessionExpiryHours?: number);
    createSession(user: User, token: string, refreshToken: string, ipAddress?: string, userAgent?: string): Promise<Session>;
    getSession(sessionId: string): Promise<Session | null>;
    getSessionByToken(token: string): Promise<Session | null>;
    refreshSession(refreshToken: string): Promise<{
        session: Session;
        newToken: string;
    }>;
    invalidateSession(sessionId: string): Promise<void>;
    invalidateAllUserSessions(userId: string): Promise<void>;
    cleanupExpiredSessions(): Promise<number>;
    getUserActiveSessions(userId: string): Promise<Session[]>;
    private generateNewToken;
    private generateNewRefreshToken;
}
//# sourceMappingURL=SessionService.d.ts.map