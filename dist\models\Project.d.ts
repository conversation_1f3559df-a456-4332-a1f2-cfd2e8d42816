import { Project as IProject, ProjectPermission, ProjectData } from '../types';
export declare class Project implements IProject {
    id: string;
    name: string;
    description: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
    status: 'active' | 'archived';
    metadata: Record<string, any>;
    permissions: ProjectPermission[];
    constructor(data: IProject);
    static validate(data: Partial<IProject>): {
        isValid: boolean;
        errors: string[];
    };
    static create(data: ProjectData & {
        createdBy: string;
        id?: string;
    }): Project;
    update(updates: Partial<Pick<IProject, 'name' | 'description' | 'status' | 'metadata'>>): void;
    addPermission(permission: Omit<ProjectPermission, 'grantedAt'>): void;
    removePermission(userId: string): void;
    archive(): void;
    activate(): void;
}
//# sourceMappingURL=Project.d.ts.map