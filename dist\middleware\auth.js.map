{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AAmNA,oDAKC;AApMD,MAAa,cAAc;IAIzB,YAAY,WAAkC,EAAE,YAAkC;QAChF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAKD,YAAY;QACV,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;gBAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE;4BACL,IAAI,EAAE,cAAc;4BACpB,OAAO,EAAE,yCAAyC;4BAClD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEtC,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBACzD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;oBAChB,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE;4BACL,IAAI,EAAE,eAAe;4BACrB,OAAO,EAAE,0BAA0B;4BACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE;wBACL,IAAI,EAAE,gBAAgB;wBACtB,OAAO,EAAE,sBAAsB;wBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAKD,SAAS,CAAC,UAAiC,EAAE;QAC3C,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC;gBACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;oBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE;4BACL,IAAI,EAAE,cAAc;4BACpB,OAAO,EAAE,wBAAwB;4BACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;gBAG1D,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC7B,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;gBAGD,IAAI,UAA8B,CAAC;gBACnC,IAAI,eAAe,EAAE,CAAC;oBACpB,UAAU,GAAI,GAAG,CAAC,MAAc,CAAC,eAAe,CAAC,IAAK,GAAG,CAAC,IAAY,CAAC,eAAe,CAAC,IAAK,GAAG,CAAC,KAAa,CAAC,eAAe,CAAW,CAAC;gBAC3I,CAAC;gBAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CACzD,GAAG,CAAC,IAAI,EACR,QAAQ,EACR,UAAU,EACV,UAAU,CACX,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE;4BACL,IAAI,EAAE,WAAW;4BACjB,OAAO,EAAE,gCAAgC,UAAU,cAAc,QAAQ,EAAE;4BAC3E,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE;wBACL,IAAI,EAAE,gBAAgB;wBACtB,OAAO,EAAE,qBAAqB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,QAAQ;YAClB,UAAU,EAAE,OAAO;SACpB,CAAC,CAAC;IACL,CAAC;IAKD,oBAAoB,CAAC,aAAyB,MAAM;QAClD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,SAAS;YACnB,UAAU;YACV,eAAe,EAAE,WAAW;SAC7B,CAAC,CAAC;IACL,CAAC;IAKD,iBAAiB,CAAC,aAAyB,MAAM;QAC/C,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,MAAM;YAChB,UAAU;YACV,eAAe,EAAE,QAAQ;SAC1B,CAAC,CAAC;IACL,CAAC;IAKD,kBAAkB;QAChB,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC;gBACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;oBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE;4BACL,IAAI,EAAE,cAAc;4BACpB,OAAO,EAAE,wBAAwB;4BACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,YAAY,GAAI,GAAG,CAAC,MAAc,CAAC,MAAM,IAAK,GAAG,CAAC,IAAY,CAAC,MAAM,CAAC;gBAG5E,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC9D,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;gBAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE;wBACL,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,uDAAuD;wBAChE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE;wBACL,IAAI,EAAE,gBAAgB;wBACtB,OAAO,EAAE,qBAAqB;wBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF;AA1LD,wCA0LC;AAKD,SAAgB,oBAAoB,CAClC,WAAkC,EAClC,YAAkC;IAElC,OAAO,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACvD,CAAC;AAMM,MAAM,IAAI,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC5E,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE;oBACL,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,yCAAyC;oBAClD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAKtC,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAC3B,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,cAAc;gBAClB,QAAQ,EAAE,UAAU;gBACpB,KAAK,EAAE,kBAAkB;gBACzB,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,0BAA0B;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE;gBACL,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,sBAAsB;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA9CW,QAAA,IAAI,QA8Cf"}