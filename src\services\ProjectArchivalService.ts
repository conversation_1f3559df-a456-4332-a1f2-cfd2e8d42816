import { Pool } from 'pg';
import * as path from 'path';
import * as fs from 'fs';
import { promisify } from 'util';
import { ProjectManagementError } from './ProjectManagementService';
import { AuthorizationService } from './AuthorizationService';
import { User } from '../types';

const access = promisify(fs.access);
const mkdir = promisify(fs.mkdir);
const copyFile = promisify(fs.copyFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const rmdir = promisify(fs.rmdir);

/**
 * Archive record interface
 */
export interface ArchiveRecord {
  id: string;
  projectId: string;
  archiveType: 'manual' | 'automatic' | 'scheduled';
  archivePath: string;
  archivedBy: string;
  archivedAt: Date;
  reason?: string;
  metadata: Record<string, any>;
}

/**
 * Archive restoration options
 */
export interface RestoreOptions {
  restoreFiles: boolean;
  restoreStructure: boolean;
  restorePermissions: boolean;
  targetProjectId?: string;
}

/**
 * Project archival and restoration service
 * Handles comprehensive project archival with file backup and restoration
 */
export class ProjectArchivalService {
  private db: Pool;
  private authService: AuthorizationService;
  private archivePath: string;

  constructor(
    database: Pool,
    authorizationService: AuthorizationService,
    archiveStoragePath: string = './storage/archives'
  ) {
    this.db = database;
    this.authService = authorizationService;
    this.archivePath = archiveStoragePath;
  }

  /**
   * Creates a comprehensive archive of a project
   */
  async createProjectArchive(
    projectId: string,
    archivedBy: string,
    archiveType: 'manual' | 'automatic' | 'scheduled' = 'manual',
    reason?: string
  ): Promise<ArchiveRecord> {
    try {
      // Check permissions
      const hasPermission = await this.authService.hasPermission(
        { id: archivedBy } as User,
        'project',
        'admin',
        projectId
      );

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to archive project',
          'PERMISSION_DENIED',
          projectId,
          archivedBy
        );
      }

      // Get project information
      const projectResult = await this.db.query(
        'SELECT * FROM projects WHERE id = $1',
        [projectId]
      );

      if (projectResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Project not found',
          'NOT_FOUND',
          projectId,
          archivedBy
        );
      }

      const project = projectResult.rows[0];

      // Create archive directory
      const archiveId = crypto.randomUUID();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const archiveDir = path.join(this.archivePath, `${project.name}_${timestamp}_${archiveId}`);
      
      await mkdir(archiveDir, { recursive: true });

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Archive project metadata
        const metadataPath = path.join(archiveDir, 'project_metadata.json');
        const projectMetadata = {
          project,
          archivedAt: new Date(),
          archivedBy,
          reason,
          archiveType
        };

        await fs.promises.writeFile(metadataPath, JSON.stringify(projectMetadata, null, 2));

        // Archive project structure
        await this.archiveProjectStructure(projectId, archiveDir);

        // Archive project files
        await this.archiveProjectFiles(projectId, archiveDir);

        // Archive project permissions
        await this.archiveProjectPermissions(projectId, archiveDir);

        // Create archive record
        const archiveRecord: ArchiveRecord = {
          id: archiveId,
          projectId,
          archiveType,
          archivePath: archiveDir,
          archivedBy,
          archivedAt: new Date(),
          reason,
          metadata: {
            projectName: project.name,
            originalCreatedBy: project.created_by,
            originalCreatedAt: project.created_at,
            fileCount: await this.getProjectFileCount(projectId),
            totalSize: await this.getProjectTotalSize(projectId)
          }
        };

        // Insert archive record
        await client.query(
          `INSERT INTO project_archives 
           (id, project_id, archive_type, archive_path, archived_by, archived_at, reason, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            archiveRecord.id,
            archiveRecord.projectId,
            archiveRecord.archiveType,
            archiveRecord.archivePath,
            archiveRecord.archivedBy,
            archiveRecord.archivedAt,
            archiveRecord.reason,
            JSON.stringify(archiveRecord.metadata)
          ]
        );

        // Update project status to archived
        await client.query(
          'UPDATE projects SET status = $1, updated_at = $2 WHERE id = $3',
          ['archived', new Date(), projectId]
        );

        await client.query('COMMIT');

        return archiveRecord;

      } catch (error) {
        await client.query('ROLLBACK');
        
        // Clean up archive directory on error
        try {
          await rmdir(archiveDir, { recursive: true });
        } catch (cleanupError) {
          console.warn('Failed to cleanup archive directory:', cleanupError);
        }
        
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to create project archive: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ARCHIVE_ERROR',
        projectId,
        archivedBy
      );
    }
  }

  /**
   * Archives project folder structure
   */
  private async archiveProjectStructure(projectId: string, archiveDir: string): Promise<void> {
    const structureResult = await this.db.query(
      'SELECT * FROM project_folders WHERE project_id = $1 ORDER BY path',
      [projectId]
    );

    const structurePath = path.join(archiveDir, 'project_structure.json');
    await fs.promises.writeFile(
      structurePath,
      JSON.stringify(structureResult.rows, null, 2)
    );
  }

  /**
   * Archives project files metadata and copies physical files
   */
  private async archiveProjectFiles(projectId: string, archiveDir: string): Promise<void> {
    const filesResult = await this.db.query(
      'SELECT * FROM files WHERE project_id = $1 AND is_deleted = FALSE',
      [projectId]
    );

    const filesMetadataPath = path.join(archiveDir, 'files_metadata.json');
    await fs.promises.writeFile(
      filesMetadataPath,
      JSON.stringify(filesResult.rows, null, 2)
    );

    // Create files directory in archive
    const filesArchiveDir = path.join(archiveDir, 'files');
    await mkdir(filesArchiveDir, { recursive: true });

    // Copy physical files
    for (const file of filesResult.rows) {
      try {
        const sourcePath = path.join('./storage/files', file.id);
        const targetPath = path.join(filesArchiveDir, `${file.id}_${file.original_name}`);
        
        await access(sourcePath);
        await copyFile(sourcePath, targetPath);
      } catch (error) {
        console.warn(`Failed to archive file ${file.id}:`, error);
      }
    }
  }

  /**
   * Archives project permissions
   */
  private async archiveProjectPermissions(projectId: string, archiveDir: string): Promise<void> {
    const permissionsResult = await this.db.query(
      'SELECT * FROM project_permissions WHERE project_id = $1',
      [projectId]
    );

    const permissionsPath = path.join(archiveDir, 'project_permissions.json');
    await fs.promises.writeFile(
      permissionsPath,
      JSON.stringify(permissionsResult.rows, null, 2)
    );
  }

  /**
   * Gets the total number of files in a project
   */
  private async getProjectFileCount(projectId: string): Promise<number> {
    const result = await this.db.query(
      'SELECT COUNT(*) as count FROM files WHERE project_id = $1 AND is_deleted = FALSE',
      [projectId]
    );
    return parseInt(result.rows[0].count);
  }

  /**
   * Gets the total size of all files in a project
   */
  private async getProjectTotalSize(projectId: string): Promise<number> {
    const result = await this.db.query(
      'SELECT COALESCE(SUM(size), 0) as total_size FROM files WHERE project_id = $1 AND is_deleted = FALSE',
      [projectId]
    );
    return parseInt(result.rows[0].total_size);
  }

  /**
   * Restores a project from archive
   */
  async restoreProjectFromArchive(
    archiveId: string,
    restoredBy: string,
    options: RestoreOptions = {
      restoreFiles: true,
      restoreStructure: true,
      restorePermissions: true
    }
  ): Promise<string> {
    try {
      // Get archive record
      const archiveResult = await this.db.query(
        'SELECT * FROM project_archives WHERE id = $1',
        [archiveId]
      );

      if (archiveResult.rows.length === 0) {
        throw new ProjectManagementError(
          'Archive not found',
          'NOT_FOUND',
          undefined,
          restoredBy
        );
      }

      const archive = archiveResult.rows[0];

      // Check permissions (user must be admin or the original archiver)
      const hasPermission = await this.authService.hasPermission(
        { id: restoredBy } as User,
        'system',
        'admin'
      ) || archive.archived_by === restoredBy;

      if (!hasPermission) {
        throw new ProjectManagementError(
          'Insufficient permissions to restore project',
          'PERMISSION_DENIED',
          archive.project_id,
          restoredBy
        );
      }

      // Check if archive directory exists
      await access(archive.archive_path);

      const client = await this.db.connect();
      
      try {
        await client.query('BEGIN');

        // Restore project
        const projectId = options.targetProjectId || archive.project_id;
        
        // Read project metadata from archive
        const metadataPath = path.join(archive.archive_path, 'project_metadata.json');
        const projectMetadata = JSON.parse(await fs.promises.readFile(metadataPath, 'utf-8'));

        // Restore project record
        await client.query(
          `INSERT INTO projects (id, name, description, created_by, created_at, updated_at, status, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
           ON CONFLICT (id) DO UPDATE SET 
           status = 'active', updated_at = $6`,
          [
            projectId,
            projectMetadata.project.name + (options.targetProjectId ? '_restored' : ''),
            projectMetadata.project.description,
            projectMetadata.project.created_by,
            projectMetadata.project.created_at,
            new Date(),
            'active',
            JSON.stringify(projectMetadata.project.metadata)
          ]
        );

        // Restore project structure if requested
        if (options.restoreStructure) {
          await this.restoreProjectStructure(archive.archive_path, projectId, client);
        }

        // Restore project files if requested
        if (options.restoreFiles) {
          await this.restoreProjectFiles(archive.archive_path, projectId, client);
        }

        // Restore project permissions if requested
        if (options.restorePermissions) {
          await this.restoreProjectPermissions(archive.archive_path, projectId, client);
        }

        await client.query('COMMIT');

        return projectId;

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }

    } catch (error) {
      if (error instanceof ProjectManagementError) {
        throw error;
      }
      
      throw new ProjectManagementError(
        `Failed to restore project: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'RESTORE_ERROR',
        undefined,
        restoredBy
      );
    }
  }

  /**
   * Restores project structure from archive
   */
  private async restoreProjectStructure(archivePath: string, projectId: string, client: any): Promise<void> {
    const structurePath = path.join(archivePath, 'project_structure.json');
    
    try {
      const structureData = JSON.parse(await fs.promises.readFile(structurePath, 'utf-8'));
      
      for (const folder of structureData) {
        await client.query(
          `INSERT INTO project_folders (id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
           ON CONFLICT (id) DO NOTHING`,
          [
            folder.id,
            projectId,
            folder.name,
            folder.parent_id,
            folder.path.replace(folder.project_id, projectId),
            folder.created_by,
            folder.created_at,
            folder.updated_at,
            JSON.stringify(folder.metadata)
          ]
        );
      }
    } catch (error) {
      console.warn('Failed to restore project structure:', error);
    }
  }

  /**
   * Restores project files from archive
   */
  private async restoreProjectFiles(archivePath: string, projectId: string, client: any): Promise<void> {
    const filesMetadataPath = path.join(archivePath, 'files_metadata.json');
    
    try {
      const filesData = JSON.parse(await fs.promises.readFile(filesMetadataPath, 'utf-8'));
      const filesArchiveDir = path.join(archivePath, 'files');
      
      for (const file of filesData) {
        // Restore file metadata
        await client.query(
          `INSERT INTO files (id, project_id, folder_id, name, original_name, file_type, size, checksum, uploaded_by, uploaded_at, current_version, metadata, is_deleted)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
           ON CONFLICT (id) DO NOTHING`,
          [
            file.id,
            projectId,
            file.folder_id,
            file.name,
            file.original_name,
            file.file_type,
            file.size,
            file.checksum,
            file.uploaded_by,
            file.uploaded_at,
            file.current_version,
            JSON.stringify(file.metadata),
            false
          ]
        );

        // Restore physical file
        try {
          const sourcePath = path.join(filesArchiveDir, `${file.id}_${file.original_name}`);
          const targetPath = path.join('./storage/files', file.id);
          
          await access(sourcePath);
          await copyFile(sourcePath, targetPath);
        } catch (error) {
          console.warn(`Failed to restore file ${file.id}:`, error);
        }
      }
    } catch (error) {
      console.warn('Failed to restore project files:', error);
    }
  }

  /**
   * Restores project permissions from archive
   */
  private async restoreProjectPermissions(archivePath: string, projectId: string, client: any): Promise<void> {
    const permissionsPath = path.join(archivePath, 'project_permissions.json');
    
    try {
      const permissionsData = JSON.parse(await fs.promises.readFile(permissionsPath, 'utf-8'));
      
      for (const permission of permissionsData) {
        await client.query(
          `INSERT INTO project_permissions (id, project_id, user_id, role, granted_by, granted_at)
           VALUES ($1, $2, $3, $4, $5, $6)
           ON CONFLICT (project_id, user_id) DO UPDATE SET 
           role = $4, granted_by = $5, granted_at = $6`,
          [
            permission.id,
            projectId,
            permission.user_id,
            permission.role,
            permission.granted_by,
            permission.granted_at
          ]
        );
      }
    } catch (error) {
      console.warn('Failed to restore project permissions:', error);
    }
  }
}
