import { FileMetadata } from '../types';
export declare function getFileExtension(filename: string): string;
export declare function getFileNameWithoutExtension(filename: string): string;
export declare function sanitizeFileName(filename: string): string;
export declare function generateUniqueFileName(originalName: string): string;
export declare function calculateChecksum(buffer: Buffer, algorithm?: string): string;
export declare function formatFileSize(bytes: number): string;
export declare function isValidFileExtension(filename: string, allowedExtensions: string[]): boolean;
export declare function getMimeTypeFromExtension(filename: string): string;
export declare function validateFileName(filename: string): {
    isValid: boolean;
    errors: string[];
};
export declare function generateStoragePath(projectId: string, fileId: string, version: string, originalName: string): string;
export declare function extractMetadataFromFilename(filename: string): Partial<FileMetadata>;
export declare function isBinaryFile(buffer: Buffer, sampleSize?: number): boolean;
export declare function compareVersions(version1: string, version2: string): number;
export declare function getNextVersion(currentVersion: string, type?: 'major' | 'minor' | 'patch'): string;
//# sourceMappingURL=fileUtils.d.ts.map