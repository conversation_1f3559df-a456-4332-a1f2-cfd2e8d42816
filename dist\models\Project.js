"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Project = void 0;
class Project {
    constructor(data) {
        this.id = data.id;
        this.name = data.name;
        this.description = data.description;
        this.createdBy = data.createdBy;
        this.createdAt = data.createdAt;
        this.updatedAt = data.updatedAt;
        this.status = data.status;
        this.metadata = data.metadata;
        this.permissions = data.permissions;
    }
    static validate(data) {
        const errors = [];
        if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
            errors.push('Project name is required and must be a non-empty string');
        }
        if (data.name && data.name.length > 255) {
            errors.push('Project name must not exceed 255 characters');
        }
        if (!data.description || typeof data.description !== 'string') {
            errors.push('Project description is required and must be a string');
        }
        if (!data.createdBy || typeof data.createdBy !== 'string' || data.createdBy.trim().length === 0) {
            errors.push('CreatedBy is required and must be a non-empty string');
        }
        if (data.status && !['active', 'archived'].includes(data.status)) {
            errors.push('Status must be either "active" or "archived"');
        }
        if (data.createdAt && !(data.createdAt instanceof Date)) {
            errors.push('CreatedAt must be a valid Date object');
        }
        if (data.updatedAt && !(data.updatedAt instanceof Date)) {
            errors.push('UpdatedAt must be a valid Date object');
        }
        if (data.metadata && typeof data.metadata !== 'object') {
            errors.push('Metadata must be an object');
        }
        if (data.permissions && !Array.isArray(data.permissions)) {
            errors.push('Permissions must be an array');
        }
        if (data.permissions) {
            data.permissions.forEach((permission, index) => {
                if (!permission.userId || typeof permission.userId !== 'string') {
                    errors.push(`Permission ${index}: userId is required and must be a string`);
                }
                if (!permission.role || !['admin', 'editor', 'viewer'].includes(permission.role)) {
                    errors.push(`Permission ${index}: role must be "admin", "editor", or "viewer"`);
                }
                if (!permission.grantedBy || typeof permission.grantedBy !== 'string') {
                    errors.push(`Permission ${index}: grantedBy is required and must be a string`);
                }
                if (!permission.grantedAt || !(permission.grantedAt instanceof Date)) {
                    errors.push(`Permission ${index}: grantedAt is required and must be a Date`);
                }
            });
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    static create(data) {
        const now = new Date();
        const projectData = {
            id: data.id || crypto.randomUUID(),
            name: data.name,
            description: data.description,
            createdBy: data.createdBy,
            createdAt: now,
            updatedAt: now,
            status: 'active',
            metadata: data.metadata || {},
            permissions: []
        };
        const validation = Project.validate(projectData);
        if (!validation.isValid) {
            throw new Error(`Invalid project data: ${validation.errors.join(', ')}`);
        }
        return new Project(projectData);
    }
    update(updates) {
        const updatedData = { ...this, ...updates, updatedAt: new Date() };
        const validation = Project.validate(updatedData);
        if (!validation.isValid) {
            throw new Error(`Invalid project update: ${validation.errors.join(', ')}`);
        }
        Object.assign(this, updates);
        this.updatedAt = new Date();
    }
    addPermission(permission) {
        const newPermission = {
            ...permission,
            grantedAt: new Date()
        };
        const validation = Project.validate({ ...this, permissions: [...this.permissions, newPermission] });
        if (!validation.isValid) {
            throw new Error(`Invalid permission: ${validation.errors.join(', ')}`);
        }
        this.permissions = this.permissions.filter(p => p.userId !== permission.userId);
        this.permissions.push(newPermission);
        this.updatedAt = new Date();
    }
    removePermission(userId) {
        this.permissions = this.permissions.filter(p => p.userId !== userId);
        this.updatedAt = new Date();
    }
    archive() {
        this.status = 'archived';
        this.updatedAt = new Date();
    }
    activate() {
        this.status = 'active';
        this.updatedAt = new Date();
    }
}
exports.Project = Project;
//# sourceMappingURL=Project.js.map