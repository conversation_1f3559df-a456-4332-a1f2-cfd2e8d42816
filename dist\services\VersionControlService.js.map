{"version": 3, "file": "VersionControlService.js", "sourceRoot": "", "sources": ["../../src/services/VersionControlService.ts"], "names": [], "mappings": ";;;AACA,+CAA4C;AAG5C,MAAa,qBAAqB;IAGhC,YAAY,EAAQ;QAClB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,OAAuB;QACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAG5B,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,KAAK,CAC5C,kFAAkF,EAClF,CAAC,MAAM,CAAC,CACT,CAAC;YAGF,IAAI,WAAW,GAAG,OAAO,CAAC;YAC1B,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC3D,WAAW,GAAG,iBAAO,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,KAAK,CACnC,iDAAiD,EACjD,CAAC,MAAM,CAAC,CACT,CAAC;YAEF,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,gBAAgB,MAAM,YAAY,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,QAAQ,GAAG,SAAS,MAAM,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAG/D,MAAM,WAAW,GAAa;gBAC5B,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;gBACvB,MAAM;gBACN,OAAO,EAAE,WAAW;gBACpB,SAAS,EAAE,OAAO,CAAC,UAAU;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,OAAO,EAAE,OAAO,CAAC,WAAW;gBAC5B,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,KAAK;gBACjB,QAAQ;aACT,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC;YAGzC,MAAM,MAAM,CAAC,KAAK,CAChB;+DACuD,EACvD;gBACE,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,MAAM;gBACd,OAAO,CAAC,OAAO;gBACf,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,SAAS;gBACjB,OAAO,CAAC,OAAO;gBACf,OAAO,CAAC,QAAQ;gBAChB,OAAO,CAAC,UAAU;gBAClB,OAAO,CAAC,QAAQ;gBAChB,CAAC;gBACD,EAAE;aACH,CACF,CAAC;YAGF,MAAM,MAAM,CAAC,KAAK,CAChB,qDAAqD,EACrD,CAAC,WAAW,EAAE,MAAM,CAAC,CACtB,CAAC;YAEF,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC7B,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC;;;;kCAI4B,EAC5B,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,iBAAO,CAAC;YACxC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,MAAM,EAAE,GAAG,CAAC,OAAO;YACnB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,QAAQ,EAAE,GAAG,CAAC,SAAS;YACvB,UAAU,EAAE,GAAG,CAAC,WAAW;YAC3B,QAAQ,EAAE,GAAG,CAAC,SAAS;SACxB,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,OAAe;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,4DAA4D,EAC5D,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,iBAAO,CAAC;YACjB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,MAAM,EAAE,GAAG,CAAC,OAAO;YACnB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,QAAQ,EAAE,GAAG,CAAC,SAAS;YACvB,UAAU,EAAE,GAAG,CAAC,WAAW;YAC3B,QAAQ,EAAE,GAAG,CAAC,SAAS;SACxB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,OAAe;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,gGAAgG,EAChG,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,YAAY,MAAM,8BAA8B,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,OAAe;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,mGAAmG,EACnG,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,YAAY,MAAM,8CAA8C,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,OAAe;QAClD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,sHAAsH,EACtH,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,YAAY,MAAM,gCAAgC,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,QAAgB,EAAE,QAAgB;QACtE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mCAAmC,QAAQ,KAAK,QAAQ,EAAE,CAAC,CAAC;QAC9E,CAAC;QAGD,MAAM,IAAI,GAAgB;YACxB,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE,EAAE,CAAC,OAAO;oBACnB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,OAAO,EAAE,EAAE,CAAC,OAAO;oBACnB,QAAQ,EAAE,EAAE,CAAC,QAAQ;oBACrB,UAAU,EAAE,EAAE,CAAC,UAAU;iBAC1B;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,EAAE,CAAC,OAAO;oBACnB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,SAAS,EAAE,EAAE,CAAC,SAAS;oBACvB,OAAO,EAAE,EAAE,CAAC,OAAO;oBACnB,QAAQ,EAAE,EAAE,CAAC,QAAQ;oBACrB,UAAU,EAAE,EAAE,CAAC,UAAU;iBAC1B;gBACD,iBAAiB,EAAE,iBAAO,CAAC,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC;aACnE;SACF,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,4EAA4E,EAC5E,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,iBAAO,CAAC;YACjB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,MAAM,EAAE,GAAG,CAAC,OAAO;YACnB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,QAAQ,EAAE,GAAG,CAAC,SAAS;YACvB,UAAU,EAAE,GAAG,CAAC,WAAW;YAC3B,QAAQ,EAAE,GAAG,CAAC,SAAS;SACxB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,2FAA2F,EAC3F,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,iBAAO,CAAC;YACxC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,MAAM,EAAE,GAAG,CAAC,OAAO;YACnB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,QAAQ,EAAE,GAAG,CAAC,SAAS;YACvB,UAAU,EAAE,GAAG,CAAC,WAAW;YAC3B,QAAQ,EAAE,GAAG,CAAC,SAAS;SACxB,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAe;QAEpD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAC5C,2DAA2D,EAC3D,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAChE,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,0CAA0C,EAAE,CAAC;QAClF,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACvC,sEAAsE,EACtE,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB,CAAC;QAEF,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QAED,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACtC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,kCAAkC,EAAE,CAAC;QAC1E,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CACpC,iDAAiD,EACjD,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACjF,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,mCAAmC,EAAE,CAAC;QAC3E,CAAC;QAED,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;IAC7B,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,OAAe,EAAE,OAAe;QACzE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC,4FAA4F,EAC5F,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAC3B,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,YAAY,MAAM,yBAAyB,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAOvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAChC;;;;;;;0BAOoB,EACpB,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;YAC3C,gBAAgB,EAAE,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACjD,cAAc,EAAE,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC;YAC7C,aAAa,EAAE,GAAG,CAAC,cAAc,IAAI,OAAO;YAC5C,YAAY,EAAE,GAAG,CAAC,aAAa,IAAI,OAAO;SAC3C,CAAC;IACJ,CAAC;CACF;AA9WD,sDA8WC"}