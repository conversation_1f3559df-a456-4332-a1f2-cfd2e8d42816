import { 
  FileRecord, 
  FileMetadata, 
  Project, 
  ProjectData, 
  Version, 
  VersionChanges, 
  VersionDiff, 
  E3ProjectData, 
  PDFMetadata, 
  DXFMetadata, 
  FileValidationResult,
  FileFilters,
  User
} from './index';

export interface FileManagementService {
  uploadFile(file: Express.Multer.File, projectId: string, uploadedBy: string, fileType: 'e3series' | 'pdf' | 'dxf', metadata?: FileMetadata): Promise<FileRecord>;
  downloadFile(fileId: string, version?: string): Promise<NodeJS.ReadableStream>;
  deleteFile(fileId: string): Promise<void>;
  listFiles(projectId: string, filters?: FileFilters): Promise<FileRecord[]>;
  extractMetadata(file: Express.Multer.File): Promise<FileMetadata>;
}

export interface ProjectManagementService {
  createProject(projectData: ProjectData, createdBy: string): Promise<Project>;
  updateProject(projectId: string, updates: Partial<ProjectData>, updatedBy: string): Promise<Project>;
  getProject(projectId: string, userId?: string): Promise<Project>;
  listProjects(userId: string): Promise<Project[]>;
  archiveProject(projectId: string, archivedBy: string): Promise<void>;
}

export interface VersionControlService {
  createVersion(fileId: string, changes: VersionChanges): Promise<Version>;
  getVersionHistory(fileId: string): Promise<Version[]>;
  compareVersions(fileId: string, version1: string, version2: string): Promise<VersionDiff>;
  lockVersion(fileId: string, version: string): Promise<void>;
}

export interface FileParserService {
  parseE3File(filePath: string): Promise<E3ProjectData>;
  parsePDFMetadata(filePath: string): Promise<PDFMetadata>;
  parseDXFMetadata(filePath: string): Promise<DXFMetadata>;
  validateFileFormat(file: Express.Multer.File): Promise<FileValidationResult>;
}

export interface AuthenticationService {
  authenticate(username: string, password: string): Promise<{ user: User; token: string }>;
  validateToken(token: string): Promise<User>;
  refreshToken(token: string): Promise<string>;
  logout(token: string): Promise<void>;
}