"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectHierarchyService = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const util_1 = require("util");
const ProjectManagementService_1 = require("./ProjectManagementService");
const mkdir = (0, util_1.promisify)(fs.mkdir);
const rmdir = (0, util_1.promisify)(fs.rmdir);
const access = (0, util_1.promisify)(fs.access);
class ProjectHierarchyService {
    constructor(database, authorizationService, storagePath = './storage/projects') {
        this.db = database;
        this.authService = authorizationService;
        this.baseStoragePath = storagePath;
    }
    async createProjectStructure(projectId, createdBy) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: createdBy }, 'project', 'admin', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to create project structure', 'PERMISSION_DENIED', projectId, createdBy);
            }
            const projectPath = path.join(this.baseStoragePath, projectId);
            await mkdir(projectPath, { recursive: true });
            const standardFolders = [
                { name: 'documents', description: 'Project documentation and specifications' },
                { name: 'schematics', description: 'E3.series schematic files' },
                { name: 'drawings', description: 'CAD drawings and DXF files' },
                { name: 'reports', description: 'Generated reports and exports' },
                { name: 'archives', description: 'Archived versions and backups' }
            ];
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                for (const folder of standardFolders) {
                    const folderPath = path.join(projectPath, folder.name);
                    await mkdir(folderPath, { recursive: true });
                    await client.query(`INSERT INTO project_folders (id, project_id, name, path, created_by, created_at, updated_at, metadata)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`, [
                        crypto.randomUUID(),
                        projectId,
                        folder.name,
                        folderPath,
                        createdBy,
                        new Date(),
                        new Date(),
                        JSON.stringify({ description: folder.description, isStandard: true })
                    ]);
                }
                await client.query('COMMIT');
            }
            catch (error) {
                await client.query('ROLLBACK');
                try {
                    await rmdir(projectPath, { recursive: true });
                }
                catch (cleanupError) {
                    console.warn('Failed to cleanup project directory:', cleanupError);
                }
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to create project structure: ${error instanceof Error ? error.message : 'Unknown error'}`, 'STRUCTURE_ERROR', projectId, createdBy);
        }
    }
    async createFolder(projectId, folderName, parentId, createdBy, metadata = {}) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: createdBy }, 'project', 'write', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to create folder', 'PERMISSION_DENIED', projectId, createdBy);
            }
            if (!folderName || folderName.trim().length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Folder name cannot be empty', 'VALIDATION_ERROR', projectId, createdBy);
            }
            const sanitizedName = folderName.replace(/[^a-zA-Z0-9_-]/g, '_');
            let parentPath = path.join(this.baseStoragePath, projectId);
            if (parentId) {
                const parentResult = await this.db.query('SELECT path FROM project_folders WHERE id = $1 AND project_id = $2', [parentId, projectId]);
                if (parentResult.rows.length === 0) {
                    throw new ProjectManagementService_1.ProjectManagementError('Parent folder not found', 'NOT_FOUND', projectId, createdBy);
                }
                parentPath = parentResult.rows[0].path;
            }
            const folderPath = path.join(parentPath, sanitizedName);
            try {
                await access(folderPath);
                throw new ProjectManagementService_1.ProjectManagementError('Folder already exists', 'ALREADY_EXISTS', projectId, createdBy);
            }
            catch (error) {
                if (error.code !== 'ENOENT') {
                    throw error;
                }
            }
            await mkdir(folderPath, { recursive: true });
            const folderId = crypto.randomUUID();
            const now = new Date();
            await this.db.query(`INSERT INTO project_folders (id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`, [
                folderId,
                projectId,
                sanitizedName,
                parentId || null,
                folderPath,
                createdBy,
                now,
                now,
                JSON.stringify(metadata)
            ]);
            const folder = {
                id: folderId,
                projectId,
                name: sanitizedName,
                path: folderPath,
                createdBy,
                createdAt: now,
                updatedAt: now,
                metadata
            };
            if (parentId !== undefined) {
                folder.parentId = parentId;
            }
            return folder;
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to create folder: ${error instanceof Error ? error.message : 'Unknown error'}`, 'FOLDER_ERROR', projectId, createdBy);
        }
    }
    async getProjectStructure(projectId, userId) {
        try {
            const hasPermission = await this.authService.hasPermission({ id: userId }, 'project', 'read', projectId);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to view project structure', 'PERMISSION_DENIED', projectId, userId);
            }
            const result = await this.db.query(`SELECT id, project_id, name, parent_id, path, created_by, created_at, updated_at, metadata
         FROM project_folders 
         WHERE project_id = $1 
         ORDER BY parent_id NULLS FIRST, name`, [projectId]);
            return result.rows.map(row => ({
                id: row.id,
                projectId: row.project_id,
                name: row.name,
                parentId: row.parent_id,
                path: row.path,
                createdBy: row.created_by,
                createdAt: row.created_at,
                updatedAt: row.updated_at,
                metadata: row.metadata || {}
            }));
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to get project structure: ${error instanceof Error ? error.message : 'Unknown error'}`, 'STRUCTURE_ERROR', projectId, userId);
        }
    }
    async deleteFolder(folderId, deletedBy) {
        try {
            const folderResult = await this.db.query('SELECT * FROM project_folders WHERE id = $1', [folderId]);
            if (folderResult.rows.length === 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Folder not found', 'NOT_FOUND', undefined, deletedBy);
            }
            const folder = folderResult.rows[0];
            const hasPermission = await this.authService.hasPermission({ id: deletedBy }, 'project', 'write', folder.project_id);
            if (!hasPermission) {
                throw new ProjectManagementService_1.ProjectManagementError('Insufficient permissions to delete folder', 'PERMISSION_DENIED', folder.project_id, deletedBy);
            }
            const metadata = typeof folder.metadata === 'string'
                ? JSON.parse(folder.metadata)
                : (folder.metadata || {});
            if (metadata.isStandard) {
                throw new ProjectManagementService_1.ProjectManagementError('Cannot delete standard project folders', 'INVALID_OPERATION', folder.project_id, deletedBy);
            }
            const childrenResult = await this.db.query('SELECT COUNT(*) as count FROM project_folders WHERE parent_id = $1', [folderId]);
            if (parseInt(childrenResult.rows[0].count) > 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Cannot delete folder with subfolders', 'INVALID_OPERATION', folder.project_id, deletedBy);
            }
            const filesResult = await this.db.query('SELECT COUNT(*) as count FROM files WHERE folder_id = $1 AND is_deleted = FALSE', [folderId]);
            if (parseInt(filesResult.rows[0].count) > 0) {
                throw new ProjectManagementService_1.ProjectManagementError('Cannot delete folder containing files', 'INVALID_OPERATION', folder.project_id, deletedBy);
            }
            const client = await this.db.connect();
            try {
                await client.query('BEGIN');
                await client.query('DELETE FROM project_folders WHERE id = $1', [folderId]);
                try {
                    await rmdir(folder.path);
                }
                catch (fsError) {
                    console.warn('Failed to delete physical folder:', fsError);
                }
                await client.query('COMMIT');
            }
            catch (error) {
                await client.query('ROLLBACK');
                throw error;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            if (error instanceof ProjectManagementService_1.ProjectManagementError) {
                throw error;
            }
            throw new ProjectManagementService_1.ProjectManagementError(`Failed to delete folder: ${error instanceof Error ? error.message : 'Unknown error'}`, 'DELETE_ERROR', undefined, deletedBy);
        }
    }
}
exports.ProjectHierarchyService = ProjectHierarchyService;
//# sourceMappingURL=ProjectHierarchyService.js.map